"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/app/ui/maintenance/list/list.tsx":
/*!**********************************************!*\
  !*** ./src/app/ui/maintenance/list/list.tsx ***!
  \**********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MaintenanceTable: function() { return /* binding */ MaintenanceTable; },\n/* harmony export */   StatusBadge: function() { return /* binding */ StatusBadge; },\n/* harmony export */   createMaintenanceTableColumns: function() { return /* binding */ createMaintenanceTableColumns; },\n/* harmony export */   \"default\": function() { return /* binding */ TaskList; },\n/* harmony export */   getVesselDetails: function() { return /* binding */ getVesselDetails; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/react/hooks/useLazyQuery.js\");\n/* harmony import */ var _app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/app/lib/graphQL/query */ \"(app-pages-browser)/./src/app/lib/graphQL/query/index.ts\");\n/* harmony import */ var _components_skeletons__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/skeletons */ \"(app-pages-browser)/./src/components/skeletons.tsx\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/api/link.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! dayjs */ \"(app-pages-browser)/./node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/dayjs.min.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(dayjs__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var lodash_isEmpty__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! lodash/isEmpty */ \"(app-pages-browser)/./node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/isEmpty.js\");\n/* harmony import */ var lodash_isEmpty__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(lodash_isEmpty__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/app/helpers/userHelper */ \"(app-pages-browser)/./src/app/helpers/userHelper.ts\");\n/* harmony import */ var _reporting_maintenance_status_activity_report__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../../reporting/maintenance-status-activity-report */ \"(app-pages-browser)/./src/app/ui/reporting/maintenance-status-activity-report.tsx\");\n/* harmony import */ var _app_helpers_csvHelper__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/app/helpers/csvHelper */ \"(app-pages-browser)/./src/app/helpers/csvHelper.ts\");\n/* harmony import */ var _app_helpers_pdfHelper__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/app/helpers/pdfHelper */ \"(app-pages-browser)/./src/app/helpers/pdfHelper.ts\");\n/* harmony import */ var _components_filteredTable__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/filteredTable */ \"(app-pages-browser)/./src/components/filteredTable.tsx\");\n/* harmony import */ var _components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/data-table-sort-header */ \"(app-pages-browser)/./src/components/data-table-sort-header.tsx\");\n/* harmony import */ var _components_filter_components_maintenance_actions__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/components/filter/components/maintenance-actions */ \"(app-pages-browser)/./src/components/filter/components/maintenance-actions.tsx\");\n/* harmony import */ var _components_ui_list_header__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/components/ui/list-header */ \"(app-pages-browser)/./src/components/ui/list-header.tsx\");\n/* harmony import */ var _components_ui_avatar__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/components/ui/avatar */ \"(app-pages-browser)/./src/components/ui/avatar.tsx\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.ts\");\n/* harmony import */ var _vessels_vesel_icon__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! ../../vessels/vesel-icon */ \"(app-pages-browser)/./src/app/ui/vessels/vesel-icon.tsx\");\n/* harmony import */ var _app_lib_vessel_icon_helper__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @/app/lib/vessel-icon-helper */ \"(app-pages-browser)/./src/app/lib/vessel-icon-helper.tsx\");\n/* harmony import */ var _components_hooks_useBreakpoints__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! @/components/hooks/useBreakpoints */ \"(app-pages-browser)/./src/components/hooks/useBreakpoints.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _app_lib_icons__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! @/app/lib/icons */ \"(app-pages-browser)/./src/app/lib/icons/index.ts\");\n/* harmony import */ var _queries__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! ./queries */ \"(app-pages-browser)/./src/app/ui/maintenance/list/queries.ts\");\n/* harmony import */ var _app_offline_models_vessel__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! @/app/offline/models/vessel */ \"(app-pages-browser)/./src/app/offline/models/vessel.js\");\n/* harmony import */ var _app_lib_utils__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! @/app/lib/utils */ \"(app-pages-browser)/./src/app/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ getVesselDetails,createMaintenanceTableColumns,StatusBadge,MaintenanceTable,default auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// Helper functions\nconst getCrewInitials = (firstName, surname)=>{\n    var _firstName_charAt, _surname_charAt;\n    if (!firstName && !surname) return \"??\";\n    const first = (firstName === null || firstName === void 0 ? void 0 : (_firstName_charAt = firstName.charAt(0)) === null || _firstName_charAt === void 0 ? void 0 : _firstName_charAt.toUpperCase()) || \"\";\n    const last = (surname === null || surname === void 0 ? void 0 : (_surname_charAt = surname.charAt(0)) === null || _surname_charAt === void 0 ? void 0 : _surname_charAt.toUpperCase()) || \"\";\n    return \"\".concat(first).concat(last) || \"??\";\n};\nconst getVesselInitials = (title)=>{\n    if (!title) return \"??\";\n    const words = title.split(\" \").filter((word)=>word.length > 0);\n    if (words.length === 1) {\n        return words[0].substring(0, 2).toUpperCase();\n    }\n    return words.slice(0, 2).map((word)=>word.charAt(0).toUpperCase()).join(\"\");\n};\nconst getCrewDetails = (assignedToID, crewInfo)=>{\n    return crewInfo === null || crewInfo === void 0 ? void 0 : crewInfo.find((crew)=>crew.id === assignedToID.toString());\n};\nconst getVesselDetails = (vesselID, vessels)=>{\n    return vessels === null || vessels === void 0 ? void 0 : vessels.find((vessel)=>vessel.id === vesselID);\n};\n// Unified maintenance table column factory\nconst createMaintenanceTableColumns = (config)=>{\n    const { showVessel = true, showMobileCards = false, crewInfo, vessels, getVesselWithIcon, pathname, searchParams } = config;\n    return (0,_components_filteredTable__WEBPACK_IMPORTED_MODULE_12__.createColumns)([\n        // Title Column\n        {\n            accessorKey: \"title\",\n            header: (param)=>{\n                let { column } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_13__.DataTableSortHeader, {\n                    column: column,\n                    title: \"Title\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                    lineNumber: 95,\n                    columnNumber: 17\n                }, undefined);\n            },\n            cell: (param)=>{\n                let { row } = param;\n                var _maintenanceCheck_isOverDue, _maintenanceCheck_basicComponent, _maintenanceCheck_assignedTo;\n                const maintenanceCheck = row.original;\n                const overDueStatus = (_maintenanceCheck_isOverDue = maintenanceCheck.isOverDue) === null || _maintenanceCheck_isOverDue === void 0 ? void 0 : _maintenanceCheck_isOverDue.status;\n                if (showMobileCards) {\n                    var _maintenanceCheck_assignedTo1, _maintenanceCheck_inventory, _maintenanceCheck_assignedTo2, _maintenanceCheck_basicComponent1;\n                    const crewDetails = getCrewDetails(((_maintenanceCheck_assignedTo1 = maintenanceCheck.assignedTo) === null || _maintenanceCheck_assignedTo1 === void 0 ? void 0 : _maintenanceCheck_assignedTo1.id) || 0, crewInfo);\n                    var _maintenanceCheck_name, _maintenanceCheck_name1;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_21__.Card, {\n                                className: \"xs:hidden space-y-3 p-2.5 min-h-20 w-full shadow-none rounded-none bg-transparent\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_21__.CardContent, {\n                                    className: \"p-0 space-y-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                            href: \"/maintenance?taskID=\".concat(maintenanceCheck.id, \"&redirect_to=\").concat(pathname, \"?\").concat(searchParams.toString()),\n                                            className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_25__.cn)(\"text-base\", overDueStatus === \"High\" ? \"text-cinnabar-500 hover:text-cinnabar-700\" : overDueStatus === \"Upcoming\" ? \"text-fire-bush-500 hover:text-fire-bush-700\" : \"hover:text-curious-blue-400\"),\n                                            children: (_maintenanceCheck_name = maintenanceCheck.name) !== null && _maintenanceCheck_name !== void 0 ? _maintenanceCheck_name : \"Task #\".concat(maintenanceCheck.id, \" (No Name) - \").concat(dayjs__WEBPACK_IMPORTED_MODULE_5___default()(maintenanceCheck.created).format(\"DD/MM/YYYY\"))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                            lineNumber: 112,\n                                            columnNumber: 37\n                                        }, undefined),\n                                        ((_maintenanceCheck_inventory = maintenanceCheck.inventory) === null || _maintenanceCheck_inventory === void 0 ? void 0 : _maintenanceCheck_inventory.id) > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-col\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xs text-muted-foreground\",\n                                                    children: \"Inventory item\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                                    lineNumber: 131,\n                                                    columnNumber: 45\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                    href: \"/inventory/view?id=\".concat(maintenanceCheck.inventory.id),\n                                                    className: \"hover:underline text-sm\",\n                                                    children: maintenanceCheck.inventory.item\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                                    lineNumber: 134,\n                                                    columnNumber: 45\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                            lineNumber: 130,\n                                            columnNumber: 41\n                                        }, undefined),\n                                        ((_maintenanceCheck_assignedTo2 = maintenanceCheck.assignedTo) === null || _maintenanceCheck_assignedTo2 === void 0 ? void 0 : _maintenanceCheck_assignedTo2.id) > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_16__.Avatar, {\n                                            variant: \"secondary\",\n                                            className: \"h-8 w-8\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_16__.AvatarFallback, {\n                                                className: \"text-xs\",\n                                                children: getCrewInitials(crewDetails === null || crewDetails === void 0 ? void 0 : crewDetails.firstName, crewDetails === null || crewDetails === void 0 ? void 0 : crewDetails.surname)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                                lineNumber: 150,\n                                                columnNumber: 45\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                            lineNumber: 147,\n                                            columnNumber: 41\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                    lineNumber: 111,\n                                    columnNumber: 33\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                lineNumber: 110,\n                                columnNumber: 29\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"hidden xs:block\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center flex-nowrap gap-2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                            href: \"/maintenance?taskID=\".concat(maintenanceCheck.id, \"&redirect_to=\").concat(pathname, \"?\").concat(searchParams.toString()),\n                                            className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_25__.cn)(\"text-base\", overDueStatus === \"High\" ? \"text-cinnabar-500 hover:text-cinnabar-700\" : overDueStatus === \"Upcoming\" ? \"text-fire-bush-500 hover:text-fire-bush-700\" : \"hover:text-curious-blue-400\"),\n                                            children: (_maintenanceCheck_name1 = maintenanceCheck.name) !== null && _maintenanceCheck_name1 !== void 0 ? _maintenanceCheck_name1 : \"Task #\".concat(maintenanceCheck.id, \" (No Name) - \").concat(dayjs__WEBPACK_IMPORTED_MODULE_5___default()(maintenanceCheck.created).format(\"DD/MM/YYYY\"))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                            lineNumber: 164,\n                                            columnNumber: 37\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                        lineNumber: 163,\n                                        columnNumber: 33\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"md:hidden\",\n                                        children: ((_maintenanceCheck_basicComponent1 = maintenanceCheck.basicComponent) === null || _maintenanceCheck_basicComponent1 === void 0 ? void 0 : _maintenanceCheck_basicComponent1.id) > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                            href: \"/vessel/info?id=\".concat(maintenanceCheck.basicComponent.id),\n                                            className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_25__.cn)(\"text-base\", overDueStatus === \"High\" ? \"text-cinnabar-500 hover:text-cinnabar-700\" : overDueStatus === \"Upcoming\" ? \"text-fire-bush-500 hover:text-fire-bush-700\" : \"hover:text-curious-blue-400\"),\n                                            children: maintenanceCheck.basicComponent.title\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                            lineNumber: 184,\n                                            columnNumber: 41\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                        lineNumber: 181,\n                                        columnNumber: 33\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                lineNumber: 162,\n                                columnNumber: 29\n                            }, undefined)\n                        ]\n                    }, void 0, true);\n                }\n                var _maintenanceCheck_name2;\n                // Simple layout for non-mobile-card version\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                href: \"/maintenance?taskID=\".concat(maintenanceCheck.id, \"&redirect_to=\").concat(pathname, \"?\").concat(searchParams.toString()),\n                                className: \"\".concat(overDueStatus === \"High\" ? \"text-cinnabar-500 hover:text-cinnabar-700\" : overDueStatus === \"Upcoming\" ? \"text-fire-bush-500 hover:text-fire-bush-700\" : \"hover:text-curious-blue-400\"),\n                                children: (_maintenanceCheck_name2 = maintenanceCheck.name) !== null && _maintenanceCheck_name2 !== void 0 ? _maintenanceCheck_name2 : \"Task #\".concat(maintenanceCheck.id, \" (No Name) - \").concat(dayjs__WEBPACK_IMPORTED_MODULE_5___default()(maintenanceCheck.created).format(\"DD/MM/YYYY\"))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                lineNumber: 211,\n                                columnNumber: 29\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                            lineNumber: 210,\n                            columnNumber: 25\n                        }, undefined),\n                        showVessel && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:hidden\",\n                            children: ((_maintenanceCheck_basicComponent = maintenanceCheck.basicComponent) === null || _maintenanceCheck_basicComponent === void 0 ? void 0 : _maintenanceCheck_basicComponent.id) > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                href: \"/vessel/info?id=\".concat(maintenanceCheck.basicComponent.id),\n                                className: \"text-sm text-muted-foreground hover:underline\",\n                                children: maintenanceCheck.basicComponent.title\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                lineNumber: 231,\n                                columnNumber: 37\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                            lineNumber: 229,\n                            columnNumber: 29\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"md:hidden space-y-2\",\n                            children: ((_maintenanceCheck_assignedTo = maintenanceCheck.assignedTo) === null || _maintenanceCheck_assignedTo === void 0 ? void 0 : _maintenanceCheck_assignedTo.id) > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-muted-foreground\",\n                                        children: [\n                                            \"Assigned to:\",\n                                            \" \"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                        lineNumber: 244,\n                                        columnNumber: 37\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        href: \"/crew/info?id=\".concat(maintenanceCheck.assignedTo.id),\n                                        className: \"hover:underline\",\n                                        children: maintenanceCheck.assignedTo.name\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                        lineNumber: 247,\n                                        columnNumber: 37\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                lineNumber: 243,\n                                columnNumber: 33\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                            lineNumber: 241,\n                            columnNumber: 25\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                    lineNumber: 209,\n                    columnNumber: 21\n                }, undefined);\n            },\n            sortingFn: (rowA, rowB)=>{\n                var _rowA_original, _rowB_original;\n                const valueA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : _rowA_original.name) || \"\";\n                const valueB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : _rowB_original.name) || \"\";\n                return valueA.localeCompare(valueB);\n            }\n        },\n        // Location/Vessel Column (conditional)\n        ...showVessel ? [\n            {\n                accessorKey: \"location\",\n                header: (param)=>{\n                    let { column } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_13__.DataTableSortHeader, {\n                        column: column,\n                        title: \"Location\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                        lineNumber: 274,\n                        columnNumber: 27\n                    }, undefined);\n                },\n                cellAlignment: \"left\",\n                breakpoint: showMobileCards ? \"laptop\" : \"landscape\",\n                cell: (param)=>{\n                    let { row } = param;\n                    var _maintenanceCheck_basicComponent, _maintenanceCheck_basicComponent1;\n                    const maintenanceCheck = row.original;\n                    const vesselDetails = getVesselDetails(((_maintenanceCheck_basicComponent = maintenanceCheck.basicComponent) === null || _maintenanceCheck_basicComponent === void 0 ? void 0 : _maintenanceCheck_basicComponent.id) || 0, vessels);\n                    if (showMobileCards && getVesselWithIcon) {\n                        var _maintenanceCheck_basicComponent2, _maintenanceCheck_basicComponent3;\n                        const vesselWithIcon = getVesselWithIcon(((_maintenanceCheck_basicComponent2 = maintenanceCheck.basicComponent) === null || _maintenanceCheck_basicComponent2 === void 0 ? void 0 : _maintenanceCheck_basicComponent2.id) || 0, vesselDetails);\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: ((_maintenanceCheck_basicComponent3 = maintenanceCheck.basicComponent) === null || _maintenanceCheck_basicComponent3 === void 0 ? void 0 : _maintenanceCheck_basicComponent3.id) > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2.5\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_17__.Tooltip, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_17__.TooltipTrigger, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"min-w-fit\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_vessels_vesel_icon__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                        vessel: vesselWithIcon\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                                        lineNumber: 304,\n                                                        columnNumber: 59\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                                    lineNumber: 303,\n                                                    columnNumber: 55\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                                lineNumber: 302,\n                                                columnNumber: 51\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_17__.TooltipContent, {\n                                                children: maintenanceCheck.basicComponent.title\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                                lineNumber: 311,\n                                                columnNumber: 51\n                                            }, undefined)\n                                        ]\n                                    }, vesselDetails === null || vesselDetails === void 0 ? void 0 : vesselDetails.id, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                        lineNumber: 301,\n                                        columnNumber: 47\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        href: \"/vessel/info?id=\".concat(maintenanceCheck.basicComponent.id),\n                                        className: \"hover:underline\",\n                                        children: maintenanceCheck.basicComponent.title\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                        lineNumber: 319,\n                                        columnNumber: 47\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                lineNumber: 300,\n                                columnNumber: 43\n                            }, undefined)\n                        }, void 0, false);\n                    }\n                    // Simple layout\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"hidden lg:block\",\n                        children: ((_maintenanceCheck_basicComponent1 = maintenanceCheck.basicComponent) === null || _maintenanceCheck_basicComponent1 === void 0 ? void 0 : _maintenanceCheck_basicComponent1.id) > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2.5\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_16__.Avatar, {\n                                    size: \"sm\",\n                                    variant: \"secondary\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_16__.AvatarFallback, {\n                                        className: \"text-xs\",\n                                        children: getVesselInitials((vesselDetails === null || vesselDetails === void 0 ? void 0 : vesselDetails.title) || maintenanceCheck.basicComponent.title || undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                        lineNumber: 339,\n                                        columnNumber: 47\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                    lineNumber: 338,\n                                    columnNumber: 43\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    href: \"/vessel/info?id=\".concat(maintenanceCheck.basicComponent.id),\n                                    className: \"hover:underline\",\n                                    children: maintenanceCheck.basicComponent.title\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                    lineNumber: 349,\n                                    columnNumber: 43\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                            lineNumber: 337,\n                            columnNumber: 39\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                        lineNumber: 335,\n                        columnNumber: 31\n                    }, undefined);\n                },\n                sortingFn: (rowA, rowB)=>{\n                    var _rowA_original_basicComponent, _rowA_original, _rowB_original_basicComponent, _rowB_original;\n                    const valueA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : (_rowA_original_basicComponent = _rowA_original.basicComponent) === null || _rowA_original_basicComponent === void 0 ? void 0 : _rowA_original_basicComponent.title) || \"\";\n                    const valueB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : (_rowB_original_basicComponent = _rowB_original.basicComponent) === null || _rowB_original_basicComponent === void 0 ? void 0 : _rowB_original_basicComponent.title) || \"\";\n                    return valueA.localeCompare(valueB);\n                }\n            }\n        ] : [],\n        // Assigned To Column\n        {\n            accessorKey: \"assigned\",\n            header: (param)=>{\n                let { column } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_13__.DataTableSortHeader, {\n                    column: column,\n                    title: \"Assigned to\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                    lineNumber: 380,\n                    columnNumber: 17\n                }, undefined);\n            },\n            cellAlignment: \"left\",\n            breakpoint: showMobileCards ? \"tablet-md\" : \"landscape\",\n            cell: (param)=>{\n                let { row } = param;\n                var _maintenanceCheck_assignedTo, _maintenanceCheck_assignedTo1;\n                const maintenanceCheck = row.original;\n                const crewDetails = getCrewDetails(((_maintenanceCheck_assignedTo = maintenanceCheck.assignedTo) === null || _maintenanceCheck_assignedTo === void 0 ? void 0 : _maintenanceCheck_assignedTo.id) || 0, crewInfo);\n                const displayClass = showMobileCards ? \"hidden md:block\" : \"hidden md:block\";\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: displayClass,\n                    children: ((_maintenanceCheck_assignedTo1 = maintenanceCheck.assignedTo) === null || _maintenanceCheck_assignedTo1 === void 0 ? void 0 : _maintenanceCheck_assignedTo1.id) > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2.5\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_16__.Avatar, {\n                                variant: \"secondary\",\n                                className: \"h-8 w-8\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_16__.AvatarFallback, {\n                                    className: \"text-xs\",\n                                    children: getCrewInitials(crewDetails === null || crewDetails === void 0 ? void 0 : crewDetails.firstName, crewDetails === null || crewDetails === void 0 ? void 0 : crewDetails.surname)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                    lineNumber: 402,\n                                    columnNumber: 37\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                lineNumber: 401,\n                                columnNumber: 33\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                href: \"/crew/info?id=\".concat(maintenanceCheck.assignedTo.id),\n                                className: \"hover:underline \".concat(showMobileCards ? \"hidden tablet-md:block\" : \"hidden lg:block\"),\n                                children: maintenanceCheck.assignedTo.name\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                lineNumber: 409,\n                                columnNumber: 33\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                        lineNumber: 400,\n                        columnNumber: 29\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                    lineNumber: 398,\n                    columnNumber: 21\n                }, undefined);\n            },\n            sortingFn: (rowA, rowB)=>{\n                var _rowA_original_assignedTo, _rowA_original, _rowB_original_assignedTo, _rowB_original;\n                const valueA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : (_rowA_original_assignedTo = _rowA_original.assignedTo) === null || _rowA_original_assignedTo === void 0 ? void 0 : _rowA_original_assignedTo.name) || \"\";\n                const valueB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : (_rowB_original_assignedTo = _rowB_original.assignedTo) === null || _rowB_original_assignedTo === void 0 ? void 0 : _rowB_original_assignedTo.name) || \"\";\n                return valueA.localeCompare(valueB);\n            }\n        },\n        // Inventory Column\n        {\n            accessorKey: \"inventory\",\n            header: \"Inventory item\",\n            cellAlignment: \"left\",\n            breakpoint: showMobileCards ? \"phablet\" : \"tablet-lg\",\n            cell: (param)=>{\n                let { row } = param;\n                var _maintenanceCheck_inventory;\n                const maintenanceCheck = row.original;\n                const displayClass = showMobileCards ? \"\" : \"hidden md:block\";\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: displayClass,\n                    children: ((_maintenanceCheck_inventory = maintenanceCheck.inventory) === null || _maintenanceCheck_inventory === void 0 ? void 0 : _maintenanceCheck_inventory.id) > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        href: \"/inventory/view?id=\".concat(maintenanceCheck.inventory.id),\n                        className: \"hover:underline\",\n                        children: maintenanceCheck.inventory.item\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                        lineNumber: 448,\n                        columnNumber: 29\n                    }, undefined) : showMobileCards && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        children: \"-\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                        lineNumber: 454,\n                        columnNumber: 48\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                    lineNumber: 446,\n                    columnNumber: 21\n                }, undefined);\n            }\n        },\n        // Status Column\n        {\n            accessorKey: \"status\",\n            header: (param)=>{\n                let { column } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_13__.DataTableSortHeader, {\n                    column: column,\n                    title: \"Status\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                    lineNumber: 465,\n                    columnNumber: 17\n                }, undefined);\n            },\n            cellAlignment: \"right\",\n            cell: (param)=>{\n                let { row } = param;\n                const maintenanceCheck = row.original;\n                if (!maintenanceCheck) {\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: \"-\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                        lineNumber: 472,\n                        columnNumber: 28\n                    }, undefined);\n                }\n                if (showMobileCards) {\n                    var _maintenanceCheck_isOverDue, _maintenanceCheck_isOverDue1;\n                    const overDueStatus = (_maintenanceCheck_isOverDue = maintenanceCheck.isOverDue) === null || _maintenanceCheck_isOverDue === void 0 ? void 0 : _maintenanceCheck_isOverDue.status;\n                    const overDueDays = (_maintenanceCheck_isOverDue1 = maintenanceCheck.isOverDue) === null || _maintenanceCheck_isOverDue1 === void 0 ? void 0 : _maintenanceCheck_isOverDue1.day;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: overDueStatus === \"High\" ? !(0,_components_hooks_useBreakpoints__WEBPACK_IMPORTED_MODULE_20__.useBreakpoints)()[\"tablet-lg\"] ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"items-end mr-2.5 text-nowrap w-fit \".concat(overDueStatus === \"High\" ? \"alert text-sm !px-1.5 !py-0.5 xs:px-3 xs:py-1 whitespace-nowrap\" : \"\"),\n                            children: overDueDays * -1 + \" days ago\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                            lineNumber: 483,\n                            columnNumber: 37\n                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatusBadge, {\n                            maintenanceCheck: maintenanceCheck\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                            lineNumber: 492,\n                            columnNumber: 37\n                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatusBadge, {\n                            maintenanceCheck: maintenanceCheck\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                            lineNumber: 497,\n                            columnNumber: 33\n                        }, undefined)\n                    }, void 0, false);\n                }\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"hidden md:block\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatusBadge, {\n                        maintenanceCheck: maintenanceCheck\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                        lineNumber: 507,\n                        columnNumber: 25\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                    lineNumber: 506,\n                    columnNumber: 21\n                }, undefined);\n            },\n            sortingFn: (rowA, rowB)=>{\n                var _rowA_original_isOverDue, _rowA_original, _rowB_original_isOverDue, _rowB_original;\n                const valueA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : (_rowA_original_isOverDue = _rowA_original.isOverDue) === null || _rowA_original_isOverDue === void 0 ? void 0 : _rowA_original_isOverDue.days) || \"\";\n                const valueB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : (_rowB_original_isOverDue = _rowB_original.isOverDue) === null || _rowB_original_isOverDue === void 0 ? void 0 : _rowB_original_isOverDue.days) || \"\";\n                return valueA.localeCompare(valueB);\n            }\n        }\n    ]);\n};\n// Status badge component following UI standards\nconst StatusBadge = (param)=>{\n    let { maintenanceCheck } = param;\n    var _maintenanceCheck_isOverDue, _maintenanceCheck_isOverDue1, _maintenanceCheck_isOverDue2, _maintenanceCheck_isOverDue3, _maintenanceCheck_isOverDue4, _maintenanceCheck_isOverDue5, _maintenanceCheck_isOverDue6, _maintenanceCheck_isOverDue7, _maintenanceCheck_isOverDue8, _maintenanceCheck_isOverDue9;\n    const isOverdue = (maintenanceCheck === null || maintenanceCheck === void 0 ? void 0 : (_maintenanceCheck_isOverDue = maintenanceCheck.isOverDue) === null || _maintenanceCheck_isOverDue === void 0 ? void 0 : _maintenanceCheck_isOverDue.status) === \"High\";\n    // Get status text\n    let statusText = \"\";\n    if ((maintenanceCheck === null || maintenanceCheck === void 0 ? void 0 : (_maintenanceCheck_isOverDue1 = maintenanceCheck.isOverDue) === null || _maintenanceCheck_isOverDue1 === void 0 ? void 0 : _maintenanceCheck_isOverDue1.status) && [\n        \"High\",\n        \"Medium\",\n        \"Low\"\n    ].includes(maintenanceCheck.isOverDue.status)) {\n        var _maintenanceCheck_isOverDue10;\n        statusText = maintenanceCheck === null || maintenanceCheck === void 0 ? void 0 : (_maintenanceCheck_isOverDue10 = maintenanceCheck.isOverDue) === null || _maintenanceCheck_isOverDue10 === void 0 ? void 0 : _maintenanceCheck_isOverDue10.days;\n    } else if ((maintenanceCheck === null || maintenanceCheck === void 0 ? void 0 : (_maintenanceCheck_isOverDue2 = maintenanceCheck.isOverDue) === null || _maintenanceCheck_isOverDue2 === void 0 ? void 0 : _maintenanceCheck_isOverDue2.status) === \"Completed\" && (maintenanceCheck === null || maintenanceCheck === void 0 ? void 0 : (_maintenanceCheck_isOverDue3 = maintenanceCheck.isOverDue) === null || _maintenanceCheck_isOverDue3 === void 0 ? void 0 : _maintenanceCheck_isOverDue3.days) === \"Save As Draft\") {\n        var _maintenanceCheck_isOverDue11;\n        statusText = maintenanceCheck === null || maintenanceCheck === void 0 ? void 0 : (_maintenanceCheck_isOverDue11 = maintenanceCheck.isOverDue) === null || _maintenanceCheck_isOverDue11 === void 0 ? void 0 : _maintenanceCheck_isOverDue11.days;\n    } else if ((maintenanceCheck === null || maintenanceCheck === void 0 ? void 0 : (_maintenanceCheck_isOverDue4 = maintenanceCheck.isOverDue) === null || _maintenanceCheck_isOverDue4 === void 0 ? void 0 : _maintenanceCheck_isOverDue4.status) === \"Upcoming\") {\n        var _maintenanceCheck_isOverDue12;\n        statusText = maintenanceCheck === null || maintenanceCheck === void 0 ? void 0 : (_maintenanceCheck_isOverDue12 = maintenanceCheck.isOverDue) === null || _maintenanceCheck_isOverDue12 === void 0 ? void 0 : _maintenanceCheck_isOverDue12.days;\n    } else if ((maintenanceCheck === null || maintenanceCheck === void 0 ? void 0 : (_maintenanceCheck_isOverDue5 = maintenanceCheck.isOverDue) === null || _maintenanceCheck_isOverDue5 === void 0 ? void 0 : _maintenanceCheck_isOverDue5.status) === \"Completed\" && lodash_isEmpty__WEBPACK_IMPORTED_MODULE_6___default()(maintenanceCheck === null || maintenanceCheck === void 0 ? void 0 : (_maintenanceCheck_isOverDue6 = maintenanceCheck.isOverDue) === null || _maintenanceCheck_isOverDue6 === void 0 ? void 0 : _maintenanceCheck_isOverDue6.days)) {\n        var _maintenanceCheck_isOverDue13;\n        statusText = maintenanceCheck === null || maintenanceCheck === void 0 ? void 0 : (_maintenanceCheck_isOverDue13 = maintenanceCheck.isOverDue) === null || _maintenanceCheck_isOverDue13 === void 0 ? void 0 : _maintenanceCheck_isOverDue13.status;\n    } else if ((maintenanceCheck === null || maintenanceCheck === void 0 ? void 0 : (_maintenanceCheck_isOverDue7 = maintenanceCheck.isOverDue) === null || _maintenanceCheck_isOverDue7 === void 0 ? void 0 : _maintenanceCheck_isOverDue7.status) === \"Completed\" && !lodash_isEmpty__WEBPACK_IMPORTED_MODULE_6___default()(maintenanceCheck === null || maintenanceCheck === void 0 ? void 0 : (_maintenanceCheck_isOverDue8 = maintenanceCheck.isOverDue) === null || _maintenanceCheck_isOverDue8 === void 0 ? void 0 : _maintenanceCheck_isOverDue8.days) && (maintenanceCheck === null || maintenanceCheck === void 0 ? void 0 : (_maintenanceCheck_isOverDue9 = maintenanceCheck.isOverDue) === null || _maintenanceCheck_isOverDue9 === void 0 ? void 0 : _maintenanceCheck_isOverDue9.days) !== \"Save As Draft\") {\n        var _maintenanceCheck_isOverDue14;\n        statusText = maintenanceCheck === null || maintenanceCheck === void 0 ? void 0 : (_maintenanceCheck_isOverDue14 = maintenanceCheck.isOverDue) === null || _maintenanceCheck_isOverDue14 === void 0 ? void 0 : _maintenanceCheck_isOverDue14.days;\n    }\n    // Only apply styling to overdue items, others are plain text\n    if (isOverdue) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n            className: \"alert w-fit inline-block rounded-md text-nowrap mr-2.5 text-sm xs:text-base !px-1.5 !py-0.5 xs:px-3 xs:py-1\",\n            children: statusText\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n            lineNumber: 627,\n            columnNumber: 13\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n        className: \"text-nowrap text-sm xs:text-base mr-2.5 !px-1.5 !py-0.5 xs:px-3 xs:py-1\",\n        children: statusText\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n        lineNumber: 634,\n        columnNumber: 9\n    }, undefined);\n};\n_c = StatusBadge;\n// Reusable MaintenanceTable component that accepts props\nfunction MaintenanceTable(param) {\n    let { maintenanceChecks, vessels, crewInfo, showVessel = false } = param;\n    _s();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_7__.usePathname)();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_7__.useSearchParams)();\n    const { getVesselWithIcon } = (0,_app_lib_vessel_icon_helper__WEBPACK_IMPORTED_MODULE_19__.useVesselIconData)();\n    const columns = createMaintenanceTableColumns({\n        showVessel: !showVessel,\n        showMobileCards: false,\n        crewInfo,\n        vessels,\n        getVesselWithIcon,\n        pathname,\n        searchParams\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_filteredTable__WEBPACK_IMPORTED_MODULE_12__.DataTable, {\n        columns: columns,\n        data: maintenanceChecks,\n        pageSize: 20,\n        showToolbar: false\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n        lineNumber: 667,\n        columnNumber: 9\n    }, this);\n}\n_s(MaintenanceTable, \"BtAwTu7MooJrudiBzqv5a/psZQo=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_7__.usePathname,\n        next_navigation__WEBPACK_IMPORTED_MODULE_7__.useSearchParams,\n        _app_lib_vessel_icon_helper__WEBPACK_IMPORTED_MODULE_19__.useVesselIconData\n    ];\n});\n_c1 = MaintenanceTable;\nfunction TaskList() {\n    _s1();\n    var _s = $RefreshSig$();\n    const [maintenanceChecks, setMaintenanceChecks] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    const [filteredMaintenanceChecks, setFilteredMaintenanceChecks] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    const [vessels, setVessels] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    const [crewInfo, setCrewInfo] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    const [filter, setFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [keywordFilter, setKeywordFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [permissions, setPermissions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [edit_task, setEdit_task] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_7__.usePathname)();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_7__.useSearchParams)();\n    const { getVesselWithIcon } = (0,_app_lib_vessel_icon_helper__WEBPACK_IMPORTED_MODULE_19__.useVesselIconData)();\n    const bp = (0,_components_hooks_useBreakpoints__WEBPACK_IMPORTED_MODULE_20__.useBreakpoints)();\n    const init_permissions = ()=>{\n        if (permissions) {\n            if ((0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_8__.hasPermission)(\"EDIT_TASK\", permissions)) {\n                setEdit_task(true);\n            } else {\n                setEdit_task(false);\n            }\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setPermissions(_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_8__.getPermissions);\n        init_permissions();\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (permissions) {\n            init_permissions();\n        }\n    }, [\n        permissions\n    ]);\n    const [queryMaintenanceChecks] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_26__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_2__.GET_MAINTENANCE_CHECK_LIST, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            const data = response.readComponentMaintenanceCheckList[0].list;\n            if (data) {\n                handleSetMaintenanceChecks(data);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"queryMaintenanceChecks error\", error);\n        }\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isLoading) {\n            loadMaintenanceChecks();\n            setIsLoading(false);\n        }\n    }, [\n        isLoading\n    ]);\n    const loadMaintenanceChecks = async ()=>{\n        await queryMaintenanceChecks({\n            variables: {\n                inventoryID: 0,\n                vesselID: 0\n            }\n        });\n    };\n    const handleSetVessels = (vessels)=>{\n        const activeVessels = vessels.filter((vessel)=>!vessel.archived);\n        const appendedData = activeVessels.map((item)=>({\n                ...item\n            }));\n        appendedData.push({\n            title: \"Other\",\n            id: 0\n        });\n        setVessels(appendedData);\n    };\n    const getVesselList = function(handleSetVessels) {\n        let offline = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : false;\n        _s();\n        const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n        const vesselModel = new _app_offline_models_vessel__WEBPACK_IMPORTED_MODULE_24__[\"default\"]();\n        (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n            if (isLoading) {\n                loadVessels();\n                setIsLoading(false);\n            }\n        }, [\n            isLoading\n        ]);\n        const [queryVessels] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_26__.useLazyQuery)(_queries__WEBPACK_IMPORTED_MODULE_23__.ReadVessels, {\n            fetchPolicy: \"cache-and-network\",\n            onCompleted: (queryVesselResponse)=>{\n                if (queryVesselResponse.readVessels.nodes) {\n                    handleSetVessels(queryVesselResponse.readVessels.nodes);\n                }\n            },\n            onError: (error)=>{\n                console.error(\"queryVessels error\", error);\n            }\n        });\n        const loadVessels = async ()=>{\n            if (offline) {\n                const response = await vesselModel.getAll();\n                handleSetVessels(response);\n            } else {\n                await queryVessels({\n                    variables: {\n                        limit: 200,\n                        offset: 0\n                    }\n                });\n            }\n        };\n    };\n    _s(getVesselList, \"pQOK42e7v9ItR64U9CP/qx2cQME=\", false, function() {\n        return [\n            _apollo_client__WEBPACK_IMPORTED_MODULE_26__.useLazyQuery\n        ];\n    });\n    getVesselList(handleSetVessels);\n    const handleSetMaintenanceChecks = (tasks)=>{\n        setMaintenanceChecks(tasks);\n        setFilteredMaintenanceChecks(tasks);\n        const appendedData = Array.from(new Set(tasks.filter((item)=>item.assignedTo.id > 0).map((item)=>item.assignedTo.id)));\n        loadCrewMemberInfo(appendedData);\n    };\n    const [queryCrewMemberInfo] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_26__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_2__.GET_CREW_BY_IDS, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            const data = response.readSeaLogsMembers.nodes;\n            if (data) {\n                setCrewInfo(data);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"queryCrewMemberInfo error\", error);\n        }\n    });\n    const loadCrewMemberInfo = async (crewId)=>{\n        await queryCrewMemberInfo({\n            variables: {\n                crewMemberIDs: crewId.length > 0 ? crewId : [\n                    0\n                ]\n            }\n        });\n    };\n    const handleFilterOnChange = (param)=>{\n        let { type, data } = param;\n        const searchFilter = {\n            ...filter\n        };\n        let filteredTasks = maintenanceChecks || [];\n        // Vessel filter\n        if (type === \"vessel\") {\n            if (Array.isArray(data) && data.length > 0) {\n                searchFilter.basicComponentID = {\n                    in: data.map((item)=>+item.value)\n                };\n            } else if (data && !Array.isArray(data)) {\n                searchFilter.basicComponentID = {\n                    eq: +data.value\n                };\n            } else {\n                delete searchFilter.basicComponentID;\n            }\n        }\n        // Status filter\n        if (type === \"status\") {\n            if (Array.isArray(data) && data.length > 0) {\n                searchFilter.status = {\n                    in: data.map((item)=>item.value)\n                };\n            } else if (data && !Array.isArray(data)) {\n                searchFilter.status = {\n                    eq: data.value\n                };\n            } else {\n                delete searchFilter.status;\n            }\n        }\n        // Assigned member filter\n        if (type === \"member\") {\n            if (Array.isArray(data) && data.length > 0) {\n                searchFilter.assignedToID = {\n                    in: data.map((item)=>+item.value)\n                };\n            } else if (data && !Array.isArray(data)) {\n                searchFilter.assignedToID = {\n                    eq: +data.value\n                };\n            } else {\n                delete searchFilter.assignedToID;\n            }\n        }\n        // Date range\n        if (type === \"dateRange\") {\n            if ((data === null || data === void 0 ? void 0 : data.startDate) && (data === null || data === void 0 ? void 0 : data.endDate)) {\n                searchFilter.expires = {\n                    gte: data.startDate,\n                    lte: data.endDate\n                };\n            } else {\n                delete searchFilter.expires;\n            }\n        }\n        // Category\n        if (type === \"category\") {\n            if (Array.isArray(data) && data.length > 0) {\n                searchFilter.maintenanceCategoryID = {\n                    in: data.map((item)=>+item.value)\n                };\n            } else if (data && !Array.isArray(data)) {\n                searchFilter.maintenanceCategoryID = {\n                    eq: +data.value\n                };\n            } else {\n                delete searchFilter.maintenanceCategoryID;\n            }\n        }\n        // Recurring filter - handle client-side filtering\n        let recurringFilter = null;\n        if (type === \"recurring\") {\n            if (data && !Array.isArray(data)) {\n                recurringFilter = data.value;\n            }\n        }\n        // Keyword filter\n        let keyFilter = keywordFilter;\n        if (type === \"keyword\" || keyFilter && keyFilter.length > 0) {\n            var _data_value;\n            const keyword = data === null || data === void 0 ? void 0 : (_data_value = data.value) === null || _data_value === void 0 ? void 0 : _data_value.trim().toLowerCase();\n            if (keyword && keyword.length > 0) {\n                filteredTasks = filteredTasks.filter((maintenanceCheck)=>[\n                        maintenanceCheck.name,\n                        maintenanceCheck.comments,\n                        maintenanceCheck.workOrderNumber\n                    ].some((field)=>field === null || field === void 0 ? void 0 : field.toLowerCase().includes(keyword)));\n                keyFilter = data.value;\n            } else {\n                keyFilter = null;\n            }\n        }\n        // Filtering based on current searchFilter\n        // Filter by vessel (basicComponentID)\n        if (searchFilter.basicComponentID) {\n            const ids = searchFilter.basicComponentID.in || [\n                searchFilter.basicComponentID.eq\n            ];\n            filteredTasks = filteredTasks.filter((mc)=>{\n                var _mc_basicComponent;\n                return ids.includes((_mc_basicComponent = mc.basicComponent) === null || _mc_basicComponent === void 0 ? void 0 : _mc_basicComponent.id);\n            });\n        }\n        // Filter by status\n        if (searchFilter.status) {\n            const statuses = searchFilter.status.in || [\n                searchFilter.status.eq\n            ];\n            filteredTasks = filteredTasks.filter((mc)=>statuses.includes(mc.status));\n        }\n        // Filter by assignedToID\n        if (searchFilter.assignedToID) {\n            const ids = searchFilter.assignedToID.in || [\n                searchFilter.assignedToID.eq\n            ];\n            filteredTasks = filteredTasks.filter((mc)=>{\n                var _mc_assignedTo;\n                return ids.includes((_mc_assignedTo = mc.assignedTo) === null || _mc_assignedTo === void 0 ? void 0 : _mc_assignedTo.id);\n            });\n        }\n        // Filter by category\n        if (searchFilter.maintenanceCategoryID) {\n            const ids = searchFilter.maintenanceCategoryID.in || [\n                searchFilter.maintenanceCategoryID.eq\n            ];\n            filteredTasks = filteredTasks.filter((mc)=>ids.includes(mc.maintenanceCategoryID));\n        }\n        // Filter by date range\n        if (searchFilter.expires && searchFilter.expires.gte && searchFilter.expires.lte) {\n            filteredTasks = filteredTasks.filter((mc)=>dayjs__WEBPACK_IMPORTED_MODULE_5___default()(mc.startDate).isAfter(dayjs__WEBPACK_IMPORTED_MODULE_5___default()(searchFilter.expires.gte)) && dayjs__WEBPACK_IMPORTED_MODULE_5___default()(mc.startDate).isBefore(dayjs__WEBPACK_IMPORTED_MODULE_5___default()(searchFilter.expires.lte)));\n        }\n        // Filter by recurring status\n        if (recurringFilter) {\n            if (recurringFilter === \"recurring\") {\n                // Recurring tasks have recurringID > 0\n                filteredTasks = filteredTasks.filter((mc)=>mc.recurringID > 0);\n            } else if (recurringFilter === \"one-off\") {\n                // One-off tasks have recurringID = 0 or null\n                filteredTasks = filteredTasks.filter((mc)=>!mc.recurringID || mc.recurringID === 0);\n            }\n        }\n        // Set updated filters\n        setFilter(searchFilter);\n        setKeywordFilter(keyFilter);\n        setFilteredMaintenanceChecks(filteredTasks);\n    // Optionally call API: loadMaintenanceChecks(searchFilter, keyFilter);\n    };\n    const downloadCsv = ()=>{\n        if (!maintenanceChecks || !vessels) {\n            return;\n        }\n        const csvEntries = [\n            [\n                \"task\",\n                \"location\",\n                \"assigned to\",\n                \"due\"\n            ]\n        ];\n        maintenanceChecks.filter((maintenanceCheck)=>maintenanceCheck.status !== \"Save_As_Draft\").forEach((maintenanceCheck)=>{\n            var _maintenanceCheck_assignedTo, _maintenanceCheck_assignedTo1;\n            const crewDetails = getCrewDetails(((_maintenanceCheck_assignedTo = maintenanceCheck.assignedTo) === null || _maintenanceCheck_assignedTo === void 0 ? void 0 : _maintenanceCheck_assignedTo.id) || 0, crewInfo);\n            const assignedToName = crewDetails ? \"\".concat(crewDetails.firstName, \" \").concat(crewDetails.surname) : ((_maintenanceCheck_assignedTo1 = maintenanceCheck.assignedTo) === null || _maintenanceCheck_assignedTo1 === void 0 ? void 0 : _maintenanceCheck_assignedTo1.name) || \"\";\n            csvEntries.push([\n                maintenanceCheck.name,\n                (vessels === null || vessels === void 0 ? void 0 : vessels.filter((vessel)=>{\n                    var _maintenanceCheck_basicComponent;\n                    return (vessel === null || vessel === void 0 ? void 0 : vessel.id) == ((_maintenanceCheck_basicComponent = maintenanceCheck.basicComponent) === null || _maintenanceCheck_basicComponent === void 0 ? void 0 : _maintenanceCheck_basicComponent.id);\n                }).map((vessel)=>vessel.title).join(\", \")) || \"\",\n                assignedToName,\n                (0,_reporting_maintenance_status_activity_report__WEBPACK_IMPORTED_MODULE_9__.dueStatusLabel)(maintenanceCheck.isOverDue)\n            ]);\n        });\n        (0,_app_helpers_csvHelper__WEBPACK_IMPORTED_MODULE_10__.exportCsv)(csvEntries);\n    };\n    const downloadPdf = ()=>{\n        if (!maintenanceChecks || !vessels) {\n            return;\n        }\n        const headers = [\n            [\n                \"Task Name\",\n                \"Location\",\n                \"Assigned To\",\n                \"Due\"\n            ]\n        ];\n        const body = maintenanceChecks.filter((maintenanceCheck)=>maintenanceCheck.status !== \"Save_As_Draft\").map((maintenanceCheck)=>{\n            var _maintenanceCheck_assignedTo, _maintenanceCheck_assignedTo1;\n            const crewDetails = getCrewDetails(((_maintenanceCheck_assignedTo = maintenanceCheck.assignedTo) === null || _maintenanceCheck_assignedTo === void 0 ? void 0 : _maintenanceCheck_assignedTo.id) || 0, crewInfo);\n            const assignedToName = crewDetails ? \"\".concat(crewDetails.firstName, \" \").concat(crewDetails.surname) : ((_maintenanceCheck_assignedTo1 = maintenanceCheck.assignedTo) === null || _maintenanceCheck_assignedTo1 === void 0 ? void 0 : _maintenanceCheck_assignedTo1.name) || \"\";\n            return [\n                maintenanceCheck.name,\n                (vessels === null || vessels === void 0 ? void 0 : vessels.filter((vessel)=>{\n                    var _maintenanceCheck_basicComponent;\n                    return (vessel === null || vessel === void 0 ? void 0 : vessel.id) == ((_maintenanceCheck_basicComponent = maintenanceCheck.basicComponent) === null || _maintenanceCheck_basicComponent === void 0 ? void 0 : _maintenanceCheck_basicComponent.id);\n                }).map((vessel)=>vessel.title).join(\", \")) || \"\",\n                assignedToName,\n                (0,_reporting_maintenance_status_activity_report__WEBPACK_IMPORTED_MODULE_9__.dueStatusLabel)(maintenanceCheck.isOverDue)\n            ];\n        });\n        (0,_app_helpers_pdfHelper__WEBPACK_IMPORTED_MODULE_11__.exportPdfTable)({\n            headers,\n            body\n        });\n    };\n    // Use the unified column factory for the main table\n    const columns = createMaintenanceTableColumns({\n        showVessel: true,\n        showMobileCards: true,\n        crewInfo: crewInfo || [],\n        vessels: vessels || [],\n        getVesselWithIcon,\n        pathname,\n        searchParams\n    });\n    // Row status evaluator for maintenance tasks\n    const getMaintenanceRowStatus = (maintenanceCheck)=>{\n        var _maintenanceCheck_isOverDue;\n        // Skip completed, archived, or draft tasks\n        if (maintenanceCheck.status === \"Completed\" || maintenanceCheck.archived || maintenanceCheck.status === \"Save_As_Draft\") {\n            return \"normal\";\n        }\n        const overDueStatus = (_maintenanceCheck_isOverDue = maintenanceCheck.isOverDue) === null || _maintenanceCheck_isOverDue === void 0 ? void 0 : _maintenanceCheck_isOverDue.status;\n        // Use the pre-calculated status values from the system\n        switch(overDueStatus){\n            case \"High\":\n                return \"overdue\" // Red highlighting\n                ;\n            case \"Upcoming\":\n                return \"upcoming\" // Orange highlighting\n                ;\n            case \"Medium\":\n            case \"Open\":\n            default:\n                return \"normal\" // No highlighting\n                ;\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_list_header__WEBPACK_IMPORTED_MODULE_15__.ListHeader, {\n                title: \"Maintenance\",\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_lib_icons__WEBPACK_IMPORTED_MODULE_22__.SealogsMaintenanceIcon, {\n                    className: \"h-12 w-12 ring-1 p-1 rounded-full bg-[#fff]\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                    lineNumber: 1120,\n                    columnNumber: 21\n                }, void 0),\n                actions: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_filter_components_maintenance_actions__WEBPACK_IMPORTED_MODULE_14__.MaintenanceFilterActions, {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                    lineNumber: 1122,\n                    columnNumber: 26\n                }, void 0),\n                titleClassName: \"\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                lineNumber: 1117,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-16\",\n                children: maintenanceChecks && vessels ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_filteredTable__WEBPACK_IMPORTED_MODULE_12__.DataTable, {\n                    columns: columns,\n                    data: filteredMaintenanceChecks || [],\n                    pageSize: 20,\n                    onChange: handleFilterOnChange,\n                    rowStatus: getMaintenanceRowStatus\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                    lineNumber: 1127,\n                    columnNumber: 21\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_skeletons__WEBPACK_IMPORTED_MODULE_3__.TableSkeleton, {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                    lineNumber: 1135,\n                    columnNumber: 21\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                lineNumber: 1125,\n                columnNumber: 13\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s1(TaskList, \"LFHio0/i4l3OQsePNWySacFXTG4=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_7__.usePathname,\n        next_navigation__WEBPACK_IMPORTED_MODULE_7__.useSearchParams,\n        _app_lib_vessel_icon_helper__WEBPACK_IMPORTED_MODULE_19__.useVesselIconData,\n        _components_hooks_useBreakpoints__WEBPACK_IMPORTED_MODULE_20__.useBreakpoints,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_26__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_26__.useLazyQuery\n    ];\n});\n_c2 = TaskList;\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"StatusBadge\");\n$RefreshReg$(_c1, \"MaintenanceTable\");\n$RefreshReg$(_c2, \"TaskList\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/maintenance/list/list.tsx\n"));

/***/ })

});