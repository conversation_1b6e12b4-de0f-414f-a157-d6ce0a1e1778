'use client'
import React, { useEffect, useState } from 'react'
import { useLazyQuery } from '@apollo/client'
import {
    GET_CREW_BY_IDS,
    GET_MAINTENANCE_CHECK_LIST,
} from '@/app/lib/graphQL/query'
import { TableSkeleton } from '@/components/skeletons'
import Link from 'next/link'

import dayjs from 'dayjs'
import { isEmpty } from 'lodash'
import { usePathname, useSearchParams } from 'next/navigation'
import { getPermissions, hasPermission } from '@/app/helpers/userHelper'
import { dueStatusLabel } from '../../reporting/maintenance-status-activity-report'
import { exportCsv } from '@/app/helpers/csvHelper'
import { exportPdfTable } from '@/app/helpers/pdfHelper'
import {
    DataTable,
    createColumns,
    RowStatusEvaluator,
} from '@/components/filteredTable'
import { DataTableSortHeader } from '@/components/data-table-sort-header'
import { MaintenanceFilterActions } from '@/components/filter/components/maintenance-actions'
import { ListHeader } from '@/components/ui/list-header'
import { Avatar, AvatarFallback } from '@/components/ui/avatar'
import { Tooltip, TooltipContent, TooltipTrigger } from '@/components/ui'
import VesselIcon from '../../vessels/vesel-icon'
import { useVesselIconData } from '@/app/lib/vessel-icon-helper'
import { useBreakpoints } from '@/components/hooks/useBreakpoints'
import { Card, CardContent } from '@/components/ui/card'
import { SealogsMaintenanceIcon } from '@/app/lib/icons'
import { ReadVessels } from './queries'
import VesselModel from '@/app/offline/models/vessel'
import { cn } from '@/app/lib/utils'

// Helper functions
const getCrewInitials = (firstName?: string, surname?: string): string => {
    if (!firstName && !surname) return '??'
    const first = firstName?.charAt(0)?.toUpperCase() || ''
    const last = surname?.charAt(0)?.toUpperCase() || ''
    return `${first}${last}` || '??'
}

const getVesselInitials = (title?: string): string => {
    if (!title) return '??'
    const words = title.split(' ').filter((word) => word.length > 0)
    if (words.length === 1) {
        return words[0].substring(0, 2).toUpperCase()
    }
    return words
        .slice(0, 2)
        .map((word) => word.charAt(0).toUpperCase())
        .join('')
}

const getCrewDetails = (assignedToID: number, crewInfo?: CrewMember[]) => {
    return crewInfo?.find((crew) => crew.id === assignedToID.toString())
}

export const getVesselDetails = (vesselID: number, vessels?: Vessel[]) => {
    return vessels?.find((vessel) => vessel.id === vesselID)
}

// Type definitions
interface MaintenanceCheck {
    id: number
    assignedTo: {
        id: number
        name: string
    }
    basicComponent: {
        id: number
        title: string | null
    }
    inventory: {
        id: number
        item: string | null
    }
    status: string
    recurringID: number
    name: string
    created: string // e.g., "2025-06-09 02:38:36"
    severity: string // e.g., "Low"
    isOverDue: {
        status: string // e.g., "Medium"
        days: string // e.g., "Open"
        ignore: boolean
        day: number
    }
    comments: string | null
    workOrderNumber: string | null
    startDate: string // e.g., "2025-06-08 00:00:00"
    expires: string | null
    maintenanceCategoryID: number
}

type MaintenanceRowTypes = {
    id: string
    index: number
    original: MaintenanceCheck
    depth: number
    _valuesCache: Record<string, unknown>
    _uniqueValuesCache: Record<string, unknown>
    subRows: any[] // adjust if sub-row structure is known
    columnFilters: Record<string, unknown>
    columnFiltersMeta: Record<string, unknown>
    _groupingValuesCache: Record<string, unknown>
}

interface Vessel {
    id: number
    title: string
    archived?: boolean
}

interface CrewMember {
    id: string
    firstName: string
    surname: string
}

interface SearchFilter {
    basicComponentID?: { eq?: number; in?: number[] }
    status?: { eq?: string; in?: string[] }
    assignedToID?: { eq?: number; in?: number[] }
    expires?: { gte?: string; lte?: string }
    maintenanceCategoryID?: { eq?: number; in?: number[] }
}

// Status badge component following UI standards
export const StatusBadge = ({
    maintenanceCheck,
}: {
    maintenanceCheck: MaintenanceCheck
}) => {
    const isOverdue = maintenanceCheck?.isOverDue?.status === 'High'

    // Get status text
    let statusText = ''
    if (
        maintenanceCheck?.isOverDue?.status &&
        ['High', 'Medium', 'Low'].includes(maintenanceCheck.isOverDue.status)
    ) {
        statusText = maintenanceCheck?.isOverDue?.days
    } else if (
        maintenanceCheck?.isOverDue?.status === 'Completed' &&
        maintenanceCheck?.isOverDue?.days === 'Save As Draft'
    ) {
        statusText = maintenanceCheck?.isOverDue?.days
    } else if (maintenanceCheck?.isOverDue?.status === 'Upcoming') {
        statusText = maintenanceCheck?.isOverDue?.days
    } else if (
        maintenanceCheck?.isOverDue?.status === 'Completed' &&
        isEmpty(maintenanceCheck?.isOverDue?.days)
    ) {
        statusText = maintenanceCheck?.isOverDue?.status
    } else if (
        maintenanceCheck?.isOverDue?.status === 'Completed' &&
        !isEmpty(maintenanceCheck?.isOverDue?.days) &&
        maintenanceCheck?.isOverDue?.days !== 'Save As Draft'
    ) {
        statusText = maintenanceCheck?.isOverDue?.days
    }

    // Only apply styling to overdue items, others are plain text
    if (isOverdue) {
        return (
            <span className="alert w-fit inline-block rounded-md text-nowrap mr-2.5 text-sm xs:text-base !px-1.5 !py-0.5 xs:px-3 xs:py-1">
                {statusText}
            </span>
        )
    }

    return (
        <span className="text-nowrap text-sm xs:text-base mr-2.5 !px-1.5 !py-0.5 xs:px-3 xs:py-1">
            {statusText}
        </span>
    )
}

// Reusable MaintenanceTable component that accepts props
export function MaintenanceTable({
    maintenanceChecks,
    vessels,
    crewInfo,
    showVessel = false,
}: {
    maintenanceChecks: MaintenanceCheck[]
    vessels: Vessel[]
    crewInfo: CrewMember[]
    showVessel?: boolean
}) {
    const pathname = usePathname()
    const searchParams = useSearchParams()
    const { vesselIconData, getVesselWithIcon } = useVesselIconData()
    const columns = createColumns([
        {
            accessorKey: 'title',
            header: ({ column }: { column: any }) => (
                <DataTableSortHeader column={column} title="Title" />
            ),
            cell: ({ row }: { row: MaintenanceRowTypes }) => {
                const maintenanceCheck: MaintenanceCheck = row.original
                const overDueStatus = maintenanceCheck.isOverDue?.status
                return (
                    <div className="space-y-2">
                        <div className="flex items-center gap-2">
                            <Link
                                href={`/maintenance?taskID=${maintenanceCheck.id}&redirect_to=${pathname}?${searchParams.toString()}`}
                                className={`${
                                    overDueStatus === 'High'
                                        ? 'text-cinnabar-500 hover:text-cinnabar-700'
                                        : overDueStatus === 'Upcoming'
                                          ? 'text-fire-bush-500 hover:text-fire-bush-700'
                                          : 'hover:text-curious-blue-400'
                                }`}>
                                {maintenanceCheck.name ??
                                    `Task #${maintenanceCheck.id} (No Name) - ${dayjs(
                                        maintenanceCheck.created,
                                    ).format('DD/MM/YYYY')}`}
                            </Link>
                        </div>

                        {/* Mobile: Show location */}
                        {!showVessel && (
                            <div className="lg:hidden">
                                {maintenanceCheck.basicComponent?.id > 0 && (
                                    <Link
                                        href={`/vessel/info?id=${maintenanceCheck.basicComponent.id}`}
                                        className="text-sm text-muted-foreground hover:underline">
                                        {maintenanceCheck.basicComponent.title}
                                    </Link>
                                )}
                            </div>
                        )}

                        {/* Mobile: Show assigned to and status */}
                        <div className="md:hidden space-y-2">
                            {maintenanceCheck.assignedTo?.id > 0 && (
                                <div className="text-sm">
                                    <span className="text-muted-foreground">
                                        Assigned to:{' '}
                                    </span>
                                    <Link
                                        href={`/crew/info?id=${maintenanceCheck.assignedTo.id}`}
                                        className="hover:underline">
                                        {maintenanceCheck.assignedTo.name}
                                    </Link>
                                </div>
                            )}
                        </div>
                    </div>
                )
            },
            sortingFn: (
                rowA: MaintenanceRowTypes,
                rowB: MaintenanceRowTypes,
            ) => {
                const valueA = rowA?.original?.name || ''
                const valueB = rowB?.original?.name || ''
                return valueA.localeCompare(valueB)
            },
        },
        ...(showVessel
            ? []
            : [
                  {
                      accessorKey: 'location',
                      header: ({ column }: { column: any }) => (
                          <DataTableSortHeader
                              column={column}
                              title="Location"
                          />
                      ),
                      cellAlignment: 'left' as const,
                      cell: ({ row }: { row: MaintenanceRowTypes }) => {
                          const maintenanceCheck = row.original
                          const vesselDetails = getVesselDetails(
                              maintenanceCheck.basicComponent?.id || 0,
                              vessels,
                          )

                          return (
                              <div className="hidden lg:block">
                                  {maintenanceCheck.basicComponent?.id > 0 && (
                                      <div className="flex items-center gap-2.5">
                                          <Avatar size="sm" variant="secondary">
                                              <AvatarFallback className="text-xs">
                                                  {getVesselInitials(
                                                      vesselDetails?.title ||
                                                          maintenanceCheck
                                                              .basicComponent
                                                              .title ||
                                                          undefined,
                                                  )}
                                              </AvatarFallback>
                                          </Avatar>
                                          <Link
                                              href={`/vessel/info?id=${maintenanceCheck.basicComponent.id}`}
                                              className="hover:underline">
                                              {
                                                  maintenanceCheck
                                                      .basicComponent.title
                                              }
                                          </Link>
                                      </div>
                                  )}
                              </div>
                          )
                      },
                      sortingFn: (
                          rowA: MaintenanceRowTypes,
                          rowB: MaintenanceRowTypes,
                      ) => {
                          const valueA =
                              rowA?.original?.basicComponent?.title || ''
                          const valueB =
                              rowB?.original?.basicComponent?.title || ''
                          return valueA.localeCompare(valueB)
                      },
                  },
              ]),
        {
            accessorKey: 'assigned',
            header: ({ column }: { column: any }) => (
                <DataTableSortHeader column={column} title="Assigned to" />
            ),
            cellAlignment: 'left' as const,
            breakpoint: 'landscape',
            cell: ({ row }: { row: MaintenanceRowTypes }) => {
                const maintenanceCheck = row.original
                const crewDetails = getCrewDetails(
                    maintenanceCheck.assignedTo?.id || 0,
                    crewInfo,
                )

                // Temporary debug log to verify the fix
                if (maintenanceCheck.assignedTo?.id) {
                    console.log('✅ Crew lookup:', {
                        assignedToId: maintenanceCheck.assignedTo.id,
                        crewFound: !!crewDetails,
                        crewName: crewDetails
                            ? `${crewDetails.firstName} ${crewDetails.surname}`
                            : 'Not found',
                    })
                }

                return (
                    <div className="hidden md:block">
                        {maintenanceCheck.assignedTo?.id > 0 && (
                            <div className="flex items-center gap-2.5">
                                <Avatar className="h-8 w-8">
                                    <AvatarFallback className="text-xs">
                                        {getCrewInitials(
                                            crewDetails?.firstName,
                                            crewDetails?.surname,
                                        )}
                                    </AvatarFallback>
                                </Avatar>
                                <Link
                                    href={`/crew/info?id=${maintenanceCheck.assignedTo.id}`}
                                    className="hover:underline hidden lg:block">
                                    {maintenanceCheck.assignedTo.name}
                                </Link>
                            </div>
                        )}
                    </div>
                )
            },
            sortingFn: (
                rowA: MaintenanceRowTypes,
                rowB: MaintenanceRowTypes,
            ) => {
                const valueA = rowA?.original?.assignedTo?.name || ''
                const valueB = rowB?.original?.assignedTo?.name || ''
                return valueA.localeCompare(valueB)
            },
        },
        {
            accessorKey: 'inventory',
            header: 'Inventory item',
            cellAlignment: 'left' as const,
            breakpoint: 'tablet-lg',
            cell: ({ row }: { row: MaintenanceRowTypes }) => {
                const maintenanceCheck = row.original
                return (
                    <div className="hidden md:block">
                        {maintenanceCheck.inventory?.id > 0 && (
                            <Link
                                href={`/inventory/view?id=${maintenanceCheck.inventory.id}`}
                                className="hover:underline">
                                {maintenanceCheck.inventory.item}
                            </Link>
                        )}
                    </div>
                )
            },
        },
        {
            accessorKey: 'status',
            header: ({ column }: { column: any }) => (
                <DataTableSortHeader column={column} title="Status" />
            ),
            cellAlignment: 'right' as const,
            cell: ({ row }: { row: MaintenanceRowTypes }) => {
                const maintenanceCheck = row.original
                return (
                    <div className="hidden md:block">
                        <StatusBadge maintenanceCheck={maintenanceCheck} />
                    </div>
                )
            },
            sortingFn: (
                rowA: MaintenanceRowTypes,
                rowB: MaintenanceRowTypes,
            ) => {
                const valueA = rowA?.original?.isOverDue?.days || ''
                const valueB = rowB?.original?.isOverDue?.days || ''
                return valueA.localeCompare(valueB)
            },
        },
    ])

    return (
        <DataTable
            columns={columns}
            data={maintenanceChecks}
            pageSize={20}
            showToolbar={false}
        />
    )
}

export default function TaskList() {
    const [maintenanceChecks, setMaintenanceChecks] =
        useState<MaintenanceCheck[]>()
    const [filteredMaintenanceChecks, setFilteredMaintenanceChecks] =
        useState<MaintenanceCheck[]>()
    const [vessels, setVessels] = useState<Vessel[]>()
    const [crewInfo, setCrewInfo] = useState<CrewMember[]>()
    const [filter, setFilter] = useState({} as SearchFilter)
    const [isLoading, setIsLoading] = useState(true)
    const [keywordFilter, setKeywordFilter] = useState<string | null>(null)
    const [permissions, setPermissions] = useState<any>(false)
    const [edit_task, setEdit_task] = useState<boolean>(false)
    const pathname = usePathname()
    const searchParams = useSearchParams()
    const { getVesselWithIcon } = useVesselIconData()
    const bp = useBreakpoints()

    const init_permissions = () => {
        if (permissions) {
            if (hasPermission('EDIT_TASK', permissions)) {
                setEdit_task(true)
            } else {
                setEdit_task(false)
            }
        }
    }

    useEffect(() => {
        setPermissions(getPermissions)
        init_permissions()
    }, [])

    useEffect(() => {
        if (permissions) {
            init_permissions()
        }
    }, [permissions])

    const [queryMaintenanceChecks] = useLazyQuery(GET_MAINTENANCE_CHECK_LIST, {
        fetchPolicy: 'cache-and-network',
        onCompleted: (response: any) => {
            const data = response.readComponentMaintenanceCheckList[0].list
            if (data) {
                handleSetMaintenanceChecks(data)
            }
        },
        onError: (error: any) => {
            console.error('queryMaintenanceChecks error', error)
        },
    })
    useEffect(() => {
        if (isLoading) {
            loadMaintenanceChecks()
            setIsLoading(false)
        }
    }, [isLoading])
    const loadMaintenanceChecks = async () => {
        await queryMaintenanceChecks({
            variables: {
                inventoryID: 0,
                vesselID: 0,
            },
        })
    }
    const handleSetVessels = (vessels: any) => {
        const activeVessels = vessels.filter((vessel: any) => !vessel.archived)
        const appendedData = activeVessels.map((item: any) => ({
            ...item,
        }))
        appendedData.push({ title: 'Other', id: 0 })
        setVessels(appendedData)
    }

    const getVesselList = (handleSetVessels: any, offline: boolean = false) => {
        const [isLoading, setIsLoading] = useState(true)
        const vesselModel = new VesselModel()
        useEffect(() => {
            if (isLoading) {
                loadVessels()
                setIsLoading(false)
            }
        }, [isLoading])

        const [queryVessels] = useLazyQuery(ReadVessels, {
            fetchPolicy: 'cache-and-network',
            onCompleted: (queryVesselResponse: any) => {
                if (queryVesselResponse.readVessels.nodes) {
                    handleSetVessels(queryVesselResponse.readVessels.nodes)
                }
            },
            onError: (error: any) => {
                console.error('queryVessels error', error)
            },
        })
        const loadVessels = async () => {
            if (offline) {
                const response = await vesselModel.getAll()
                handleSetVessels(response)
            } else {
                await queryVessels({
                    variables: {
                        limit: 200,
                        offset: 0,
                    },
                })
            }
        }
    }
    getVesselList(handleSetVessels)

    const handleSetMaintenanceChecks = (tasks: any) => {
        setMaintenanceChecks(tasks)
        setFilteredMaintenanceChecks(tasks)
        const appendedData: number[] = Array.from(
            new Set(
                tasks
                    .filter((item: any) => item.assignedTo.id > 0)
                    .map((item: any) => item.assignedTo.id),
            ),
        )
        loadCrewMemberInfo(appendedData)
    }

    const [queryCrewMemberInfo] = useLazyQuery(GET_CREW_BY_IDS, {
        fetchPolicy: 'cache-and-network',
        onCompleted: (response: any) => {
            const data = response.readSeaLogsMembers.nodes
            if (data) {
                setCrewInfo(data)
            }
        },
        onError: (error) => {
            console.error('queryCrewMemberInfo error', error)
        },
    })
    const loadCrewMemberInfo = async (crewId: any) => {
        await queryCrewMemberInfo({
            variables: {
                crewMemberIDs: crewId.length > 0 ? crewId : [0],
            },
        })
    }
    const handleFilterOnChange = ({ type, data }: any) => {
        const searchFilter: SearchFilter = { ...filter }
        let filteredTasks = maintenanceChecks || []

        // Vessel filter
        if (type === 'vessel') {
            if (Array.isArray(data) && data.length > 0) {
                searchFilter.basicComponentID = {
                    in: data.map((item) => +item.value),
                }
            } else if (data && !Array.isArray(data)) {
                searchFilter.basicComponentID = { eq: +data.value }
            } else {
                delete searchFilter.basicComponentID
            }
        }

        // Status filter
        if (type === 'status') {
            if (Array.isArray(data) && data.length > 0) {
                searchFilter.status = { in: data.map((item) => item.value) }
            } else if (data && !Array.isArray(data)) {
                searchFilter.status = { eq: data.value }
            } else {
                delete searchFilter.status
            }
        }

        // Assigned member filter
        if (type === 'member') {
            if (Array.isArray(data) && data.length > 0) {
                searchFilter.assignedToID = {
                    in: data.map((item) => +item.value),
                }
            } else if (data && !Array.isArray(data)) {
                searchFilter.assignedToID = { eq: +data.value }
            } else {
                delete searchFilter.assignedToID
            }
        }

        // Date range
        if (type === 'dateRange') {
            if (data?.startDate && data?.endDate) {
                searchFilter.expires = {
                    gte: data.startDate,
                    lte: data.endDate,
                }
            } else {
                delete searchFilter.expires
            }
        }

        // Category
        if (type === 'category') {
            if (Array.isArray(data) && data.length > 0) {
                searchFilter.maintenanceCategoryID = {
                    in: data.map((item) => +item.value),
                }
            } else if (data && !Array.isArray(data)) {
                searchFilter.maintenanceCategoryID = { eq: +data.value }
            } else {
                delete searchFilter.maintenanceCategoryID
            }
        }

        // Recurring filter - handle client-side filtering
        let recurringFilter = null
        if (type === 'recurring') {
            if (data && !Array.isArray(data)) {
                recurringFilter = data.value
            }
        }

        // Keyword filter
        let keyFilter = keywordFilter
        if (type === 'keyword' || (keyFilter && keyFilter.length > 0)) {
            const keyword = data?.value?.trim().toLowerCase()
            if (keyword && keyword.length > 0) {
                filteredTasks = filteredTasks.filter(
                    (maintenanceCheck: MaintenanceCheck) =>
                        [
                            maintenanceCheck.name,
                            maintenanceCheck.comments,
                            maintenanceCheck.workOrderNumber,
                        ].some((field) =>
                            field?.toLowerCase().includes(keyword),
                        ),
                )
                keyFilter = data.value
            } else {
                keyFilter = null
            }
        }

        // Filtering based on current searchFilter

        // Filter by vessel (basicComponentID)
        if (searchFilter.basicComponentID) {
            const ids = searchFilter.basicComponentID.in || [
                searchFilter.basicComponentID.eq,
            ]
            filteredTasks = filteredTasks.filter((mc: MaintenanceCheck) =>
                ids.includes(mc.basicComponent?.id),
            )
        }

        // Filter by status
        if (searchFilter.status) {
            const statuses = searchFilter.status.in || [searchFilter.status.eq]
            filteredTasks = filteredTasks.filter((mc: MaintenanceCheck) =>
                statuses.includes(mc.status),
            )
        }

        // Filter by assignedToID
        if (searchFilter.assignedToID) {
            const ids = searchFilter.assignedToID.in || [
                searchFilter.assignedToID.eq,
            ]
            filteredTasks = filteredTasks.filter((mc: MaintenanceCheck) =>
                ids.includes(mc.assignedTo?.id),
            )
        }

        // Filter by category
        if (searchFilter.maintenanceCategoryID) {
            const ids = searchFilter.maintenanceCategoryID.in || [
                searchFilter.maintenanceCategoryID.eq,
            ]
            filteredTasks = filteredTasks.filter((mc: MaintenanceCheck) =>
                ids.includes(mc.maintenanceCategoryID),
            )
        }

        // Filter by date range
        if (
            searchFilter.expires &&
            searchFilter.expires.gte &&
            searchFilter.expires.lte
        ) {
            filteredTasks = filteredTasks.filter(
                (mc: MaintenanceCheck) =>
                    dayjs(mc.startDate).isAfter(
                        dayjs(searchFilter.expires!.gte),
                    ) &&
                    dayjs(mc.startDate).isBefore(
                        dayjs(searchFilter.expires!.lte),
                    ),
            )
        }

        // Filter by recurring status
        if (recurringFilter) {
            if (recurringFilter === 'recurring') {
                // Recurring tasks have recurringID > 0
                filteredTasks = filteredTasks.filter(
                    (mc: MaintenanceCheck) => mc.recurringID > 0,
                )
            } else if (recurringFilter === 'one-off') {
                // One-off tasks have recurringID = 0 or null
                filteredTasks = filteredTasks.filter(
                    (mc: MaintenanceCheck) =>
                        !mc.recurringID || mc.recurringID === 0,
                )
            }
        }

        // Set updated filters
        setFilter(searchFilter)
        setKeywordFilter(keyFilter)
        setFilteredMaintenanceChecks(filteredTasks)
        // Optionally call API: loadMaintenanceChecks(searchFilter, keyFilter);
    }

    const downloadCsv = () => {
        if (!maintenanceChecks || !vessels) {
            return
        }

        const csvEntries: string[][] = [
            ['task', 'location', 'assigned to', 'due'],
        ]

        maintenanceChecks
            .filter(
                (maintenanceCheck: MaintenanceCheck) =>
                    maintenanceCheck.status !== 'Save_As_Draft',
            )
            .forEach((maintenanceCheck: MaintenanceCheck) => {
                const crewDetails = getCrewDetails(
                    maintenanceCheck.assignedTo?.id || 0,
                    crewInfo,
                )
                const assignedToName = crewDetails
                    ? `${crewDetails.firstName} ${crewDetails.surname}`
                    : maintenanceCheck.assignedTo?.name || ''

                csvEntries.push([
                    maintenanceCheck.name,
                    vessels
                        ?.filter(
                            (vessel: Vessel) =>
                                vessel?.id ==
                                maintenanceCheck.basicComponent?.id,
                        )
                        .map((vessel: Vessel) => vessel.title)
                        .join(', ') || '',
                    assignedToName,
                    dueStatusLabel(maintenanceCheck.isOverDue),
                ])
            })

        exportCsv(csvEntries)
    }

    const downloadPdf = () => {
        if (!maintenanceChecks || !vessels) {
            return
        }

        const headers: any = [['Task Name', 'Location', 'Assigned To', 'Due']]

        const body: any = maintenanceChecks
            .filter(
                (maintenanceCheck: MaintenanceCheck) =>
                    maintenanceCheck.status !== 'Save_As_Draft',
            )
            .map((maintenanceCheck: MaintenanceCheck) => {
                const crewDetails = getCrewDetails(
                    maintenanceCheck.assignedTo?.id || 0,
                    crewInfo,
                )
                const assignedToName = crewDetails
                    ? `${crewDetails.firstName} ${crewDetails.surname}`
                    : maintenanceCheck.assignedTo?.name || ''

                return [
                    maintenanceCheck.name,
                    vessels
                        ?.filter(
                            (vessel: Vessel) =>
                                vessel?.id ==
                                maintenanceCheck.basicComponent?.id,
                        )
                        .map((vessel: Vessel) => vessel.title)
                        .join(', ') || '',
                    assignedToName,
                    dueStatusLabel(maintenanceCheck.isOverDue),
                ]
            })

        exportPdfTable({
            headers,
            body,
        })
    }

    const createMaintenanceColumns = (
        crewInfo: CrewMember[],
        getVesselWithIcon: any,
    ) =>
        createColumns([
            {
                accessorKey: 'title',
                header: ({ column }: { column: any }) => (
                    <DataTableSortHeader column={column} title="Title" />
                ),
                cell: ({ row }: { row: MaintenanceRowTypes }) => {
                    const maintenanceCheck: MaintenanceCheck = row.original
                    const crewDetails = getCrewDetails(
                        maintenanceCheck.assignedTo?.id || 0,
                        crewInfo,
                    )

                    const overDueStatus = maintenanceCheck.isOverDue?.status
                    const overDueDays = maintenanceCheck.isOverDue?.day

                    return (
                        <>
                            {/* Show card layout on xs devices */}
                            <Card className="xs:hidden space-y-3 p-2.5 min-h-20 w-full shadow-none rounded-none bg-transparent">
                                <CardContent className="p-0 space-y-1">
                                    {/* Task Title */}
                                    <Link
                                        href={`/maintenance?taskID=${maintenanceCheck.id}&redirect_to=${pathname}?${searchParams.toString()}`}
                                        className={cn(
                                            `text-base`,
                                            overDueStatus === 'High'
                                                ? 'text-cinnabar-500 hover:text-cinnabar-700'
                                                : overDueStatus === 'Upcoming'
                                                  ? 'text-fire-bush-500 hover:text-fire-bush-700'
                                                  : 'hover:text-curious-blue-400',
                                        )}>
                                        {maintenanceCheck.name ??
                                            `Task #${maintenanceCheck.id} (No Name) - ${dayjs(
                                                maintenanceCheck.created,
                                            ).format('DD/MM/YYYY')}`}{' '}
                                    </Link>

                                    {/* Inventory Item */}
                                    {maintenanceCheck.inventory?.id > 0 && (
                                        <div className="flex flex-col">
                                            <span className="text-xs text-muted-foreground">
                                                Inventory item
                                            </span>
                                            <Link
                                                href={`/inventory/view?id=${maintenanceCheck.inventory.id}`}
                                                className="hover:underline text-sm">
                                                {
                                                    maintenanceCheck.inventory
                                                        .item
                                                }
                                            </Link>
                                        </div>
                                    )}

                                    {/* Assigned To */}
                                    {maintenanceCheck.assignedTo?.id > 0 && (
                                        <Avatar
                                            variant="secondary"
                                            className="h-8 w-8">
                                            <AvatarFallback className="text-xs">
                                                {getCrewInitials(
                                                    crewDetails?.firstName,
                                                    crewDetails?.surname,
                                                )}
                                            </AvatarFallback>
                                        </Avatar>
                                    )}
                                </CardContent>
                            </Card>

                            {/* Show normal table layout on larger devices */}
                            <div className="hidden xs:block">
                                <div className="flex items-center flex-nowrap gap-2">
                                    <Link
                                        href={`/maintenance?taskID=${maintenanceCheck.id}&redirect_to=${pathname}?${searchParams.toString()}`}
                                        className={cn(
                                            `text-base`,
                                            overDueStatus === 'High'
                                                ? 'text-cinnabar-500 hover:text-cinnabar-700'
                                                : overDueStatus === 'Upcoming'
                                                  ? 'text-fire-bush-500 hover:text-fire-bush-700'
                                                  : 'hover:text-curious-blue-400',
                                        )}>
                                        {maintenanceCheck.name ??
                                            `Task #${maintenanceCheck.id} (No Name) - ${dayjs(
                                                maintenanceCheck.created,
                                            ).format('DD/MM/YYYY')}`}
                                    </Link>
                                </div>
                                {/* Mobile: Show location */}
                                <div className="md:hidden">
                                    {maintenanceCheck.basicComponent?.id >
                                        0 && (
                                        <Link
                                            href={`/vessel/info?id=${maintenanceCheck.basicComponent.id}`}
                                            className={cn(
                                                `text-base`,
                                                overDueStatus === 'High'
                                                    ? 'text-cinnabar-500 hover:text-cinnabar-700'
                                                    : overDueStatus ===
                                                        'Upcoming'
                                                      ? 'text-fire-bush-500 hover:text-fire-bush-700'
                                                      : 'hover:text-curious-blue-400',
                                            )}>
                                            {
                                                maintenanceCheck.basicComponent
                                                    .title
                                            }
                                        </Link>
                                    )}
                                </div>
                            </div>
                        </>
                    )
                },
                sortingFn: (
                    rowA: MaintenanceRowTypes,
                    rowB: MaintenanceRowTypes,
                ) => {
                    const valueA = rowA?.original?.name || ''
                    const valueB = rowB?.original?.name || ''
                    return valueA.localeCompare(valueB)
                },
            },
            {
                accessorKey: 'location',
                header: ({ column }: { column: any }) => (
                    <DataTableSortHeader column={column} title="Location" />
                ),
                cellAlignment: 'left',
                breakpoint: 'laptop',
                cell: ({ row }: { row: MaintenanceRowTypes }) => {
                    const maintenanceCheck = row.original
                    const vesselDetails = getVesselDetails(
                        maintenanceCheck.basicComponent?.id || 0,
                        vessels,
                    )

                    // Get vessel with icon data
                    const vesselWithIcon = getVesselWithIcon(
                        maintenanceCheck.basicComponent?.id || 0,
                        vesselDetails,
                    )

                    return (
                        <>
                            {maintenanceCheck.basicComponent?.id > 0 && (
                                <div className="flex items-center gap-2.5">
                                    <Tooltip key={vesselDetails?.id}>
                                        <TooltipTrigger>
                                            <div className="min-w-fit">
                                                <VesselIcon
                                                    vessel={vesselWithIcon}
                                                />
                                            </div>
                                        </TooltipTrigger>
                                        <TooltipContent>
                                            {
                                                maintenanceCheck.basicComponent
                                                    .title
                                            }
                                        </TooltipContent>
                                    </Tooltip>
                                    <Link
                                        href={`/vessel/info?id=${maintenanceCheck.basicComponent.id}`}
                                        className="hover:underline">
                                        {maintenanceCheck.basicComponent.title}
                                    </Link>
                                </div>
                            )}
                        </>
                    )
                },
                sortingFn: (
                    rowA: MaintenanceRowTypes,
                    rowB: MaintenanceRowTypes,
                ) => {
                    const valueA = rowA?.original?.basicComponent?.title || ''
                    const valueB = rowB?.original?.basicComponent?.title || ''
                    return valueA.localeCompare(valueB)
                },
            },
            {
                accessorKey: 'assigned',
                header: ({ column }: { column: any }) => (
                    <DataTableSortHeader column={column} title="Assigned to" />
                ),
                cellAlignment: 'left',
                breakpoint: 'tablet-md',
                cell: ({ row }: { row: MaintenanceRowTypes }) => {
                    const maintenanceCheck = row.original
                    const crewDetails = getCrewDetails(
                        maintenanceCheck.assignedTo?.id || 0,
                        crewInfo,
                    )

                    return (
                        <>
                            {maintenanceCheck.assignedTo?.id > 0 && (
                                <div className="flex items-center gap-2.5">
                                    <Avatar
                                        variant="secondary"
                                        className="h-8 w-8">
                                        <AvatarFallback className="text-xs">
                                            {getCrewInitials(
                                                crewDetails?.firstName,
                                                crewDetails?.surname,
                                            )}
                                        </AvatarFallback>
                                    </Avatar>
                                    <Link
                                        href={`/crew/info?id=${maintenanceCheck.assignedTo.id}`}
                                        className="hover:underline hidden tablet-md:block">
                                        {maintenanceCheck.assignedTo.name}
                                    </Link>
                                </div>
                            )}
                        </>
                    )
                },
                sortingFn: (
                    rowA: MaintenanceRowTypes,
                    rowB: MaintenanceRowTypes,
                ) => {
                    const valueA = rowA?.original?.assignedTo?.name || ''
                    const valueB = rowB?.original?.assignedTo?.name || ''
                    return valueA.localeCompare(valueB)
                },
            },
            {
                accessorKey: 'inventory',
                header: 'Inventory item',
                cellAlignment: 'left',
                breakpoint: 'phablet',
                cell: ({ row }: { row: MaintenanceRowTypes }) => {
                    const maintenanceCheck = row.original
                    return (
                        <>
                            {maintenanceCheck.inventory?.id > 0 ? (
                                <Link
                                    href={`/inventory/view?id=${maintenanceCheck.inventory.id}`}
                                    className="hover:underline">
                                    {maintenanceCheck.inventory.item}
                                </Link>
                            ) : (
                                <span>-</span>
                            )}
                        </>
                    )
                },
            },
            {
                accessorKey: 'status',
                header: ({ column }: { column: any }) => (
                    <DataTableSortHeader column={column} title="Status" />
                ),
                cellAlignment: 'right',
                cell: ({ row }: { row: MaintenanceRowTypes }) => {
                    const maintenanceCheck = row.original

                    if (!maintenanceCheck) {
                        return <div>-</div>
                    }

                    const overDueStatus = maintenanceCheck.isOverDue?.status
                    const overDueDays = maintenanceCheck.isOverDue?.day

                    return (
                        <>
                            {overDueStatus === 'High' ? (
                                !bp['tablet-lg'] ? (
                                    <div
                                        className={` items-end mr-2.5 text-nowrap w-fit
                                                    ${overDueStatus === 'High' ? 'alert text-sm !px-1.5 !py-0.5 xs:px-3 xs:py-1 whitespace-nowrap' : ''}
                                                    `}>
                                        {overDueDays * -1 + ' days ago'}
                                    </div>
                                ) : (
                                    <StatusBadge
                                        maintenanceCheck={maintenanceCheck}
                                    />
                                )
                            ) : (
                                <StatusBadge
                                    maintenanceCheck={maintenanceCheck}
                                />
                            )}
                        </>
                    )
                },
                sortingFn: (
                    rowA: MaintenanceRowTypes,
                    rowB: MaintenanceRowTypes,
                ) => {
                    const valueA = rowA?.original?.isOverDue?.days || ''
                    const valueB = rowB?.original?.isOverDue?.days || ''
                    return valueA.localeCompare(valueB)
                },
            },
        ])

    const columns = createMaintenanceColumns(crewInfo || [], getVesselWithIcon)

    // Row status evaluator for maintenance tasks
    const getMaintenanceRowStatus: RowStatusEvaluator<any> = (
        maintenanceCheck,
    ) => {
        // Skip completed, archived, or draft tasks
        if (
            maintenanceCheck.status === 'Completed' ||
            maintenanceCheck.archived ||
            maintenanceCheck.status === 'Save_As_Draft'
        ) {
            return 'normal'
        }

        const overDueStatus = maintenanceCheck.isOverDue?.status

        // Use the pre-calculated status values from the system
        switch (overDueStatus) {
            case 'High':
                return 'overdue' // Red highlighting
            case 'Upcoming':
                return 'upcoming' // Orange highlighting
            case 'Medium':
            case 'Open':
            default:
                return 'normal' // No highlighting
        }
    }

    return (
        <>
            <ListHeader
                title="Maintenance"
                icon={
                    <SealogsMaintenanceIcon className="h-12 w-12 ring-1 p-1 rounded-full bg-[#fff]" />
                }
                actions={<MaintenanceFilterActions />}
                titleClassName=""
            />
            <div className="mt-16">
                {maintenanceChecks && vessels ? (
                    <DataTable
                        columns={columns}
                        data={(filteredMaintenanceChecks as any) || []}
                        pageSize={20}
                        onChange={handleFilterOnChange}
                        rowStatus={getMaintenanceRowStatus}
                    />
                ) : (
                    <TableSkeleton />
                )}
            </div>
        </>
    )
}
