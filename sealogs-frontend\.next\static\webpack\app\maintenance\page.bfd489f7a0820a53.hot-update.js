"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/maintenance/page",{

/***/ "(app-pages-browser)/./src/app/ui/maintenance/list/list.tsx":
/*!**********************************************!*\
  !*** ./src/app/ui/maintenance/list/list.tsx ***!
  \**********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MaintenanceTable: function() { return /* binding */ MaintenanceTable; },\n/* harmony export */   StatusBadge: function() { return /* binding */ StatusBadge; },\n/* harmony export */   \"default\": function() { return /* binding */ TaskList; },\n/* harmony export */   getVesselDetails: function() { return /* binding */ getVesselDetails; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/react/hooks/useLazyQuery.js\");\n/* harmony import */ var _app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/app/lib/graphQL/query */ \"(app-pages-browser)/./src/app/lib/graphQL/query/index.ts\");\n/* harmony import */ var _components_skeletons__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/skeletons */ \"(app-pages-browser)/./src/components/skeletons.tsx\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/api/link.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! dayjs */ \"(app-pages-browser)/./node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/dayjs.min.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(dayjs__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var lodash_isEmpty__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! lodash/isEmpty */ \"(app-pages-browser)/./node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/isEmpty.js\");\n/* harmony import */ var lodash_isEmpty__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(lodash_isEmpty__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/app/helpers/userHelper */ \"(app-pages-browser)/./src/app/helpers/userHelper.ts\");\n/* harmony import */ var _reporting_maintenance_status_activity_report__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../../reporting/maintenance-status-activity-report */ \"(app-pages-browser)/./src/app/ui/reporting/maintenance-status-activity-report.tsx\");\n/* harmony import */ var _app_helpers_csvHelper__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/app/helpers/csvHelper */ \"(app-pages-browser)/./src/app/helpers/csvHelper.ts\");\n/* harmony import */ var _app_helpers_pdfHelper__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/app/helpers/pdfHelper */ \"(app-pages-browser)/./src/app/helpers/pdfHelper.ts\");\n/* harmony import */ var _components_filteredTable__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/filteredTable */ \"(app-pages-browser)/./src/components/filteredTable.tsx\");\n/* harmony import */ var _components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/data-table-sort-header */ \"(app-pages-browser)/./src/components/data-table-sort-header.tsx\");\n/* harmony import */ var _components_filter_components_maintenance_actions__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/components/filter/components/maintenance-actions */ \"(app-pages-browser)/./src/components/filter/components/maintenance-actions.tsx\");\n/* harmony import */ var _components_ui_list_header__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/components/ui/list-header */ \"(app-pages-browser)/./src/components/ui/list-header.tsx\");\n/* harmony import */ var _components_ui_avatar__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/components/ui/avatar */ \"(app-pages-browser)/./src/components/ui/avatar.tsx\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.ts\");\n/* harmony import */ var _vessels_vesel_icon__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! ../../vessels/vesel-icon */ \"(app-pages-browser)/./src/app/ui/vessels/vesel-icon.tsx\");\n/* harmony import */ var _app_lib_vessel_icon_helper__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @/app/lib/vessel-icon-helper */ \"(app-pages-browser)/./src/app/lib/vessel-icon-helper.tsx\");\n/* harmony import */ var _components_hooks_useBreakpoints__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! @/components/hooks/useBreakpoints */ \"(app-pages-browser)/./src/components/hooks/useBreakpoints.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _app_lib_icons__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! @/app/lib/icons */ \"(app-pages-browser)/./src/app/lib/icons/index.ts\");\n/* harmony import */ var _queries__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! ./queries */ \"(app-pages-browser)/./src/app/ui/maintenance/list/queries.ts\");\n/* harmony import */ var _app_offline_models_vessel__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! @/app/offline/models/vessel */ \"(app-pages-browser)/./src/app/offline/models/vessel.js\");\n/* harmony import */ var _app_lib_utils__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! @/app/lib/utils */ \"(app-pages-browser)/./src/app/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ getVesselDetails,StatusBadge,MaintenanceTable,default auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// Helper functions\nconst getCrewInitials = (firstName, surname)=>{\n    var _firstName_charAt, _surname_charAt;\n    if (!firstName && !surname) return \"??\";\n    const first = (firstName === null || firstName === void 0 ? void 0 : (_firstName_charAt = firstName.charAt(0)) === null || _firstName_charAt === void 0 ? void 0 : _firstName_charAt.toUpperCase()) || \"\";\n    const last = (surname === null || surname === void 0 ? void 0 : (_surname_charAt = surname.charAt(0)) === null || _surname_charAt === void 0 ? void 0 : _surname_charAt.toUpperCase()) || \"\";\n    return \"\".concat(first).concat(last) || \"??\";\n};\nconst getVesselInitials = (title)=>{\n    if (!title) return \"??\";\n    const words = title.split(\" \").filter((word)=>word.length > 0);\n    if (words.length === 1) {\n        return words[0].substring(0, 2).toUpperCase();\n    }\n    return words.slice(0, 2).map((word)=>word.charAt(0).toUpperCase()).join(\"\");\n};\nconst getCrewDetails = (assignedToID, crewInfo)=>{\n    return crewInfo === null || crewInfo === void 0 ? void 0 : crewInfo.find((crew)=>crew.id === assignedToID.toString());\n};\nconst getVesselDetails = (vesselID, vessels)=>{\n    return vessels === null || vessels === void 0 ? void 0 : vessels.find((vessel)=>vessel.id === vesselID);\n};\n// Status badge component following UI standards\nconst StatusBadge = (param)=>{\n    let { maintenanceCheck } = param;\n    var _maintenanceCheck_isOverDue, _maintenanceCheck_isOverDue1, _maintenanceCheck_isOverDue2, _maintenanceCheck_isOverDue3, _maintenanceCheck_isOverDue4, _maintenanceCheck_isOverDue5, _maintenanceCheck_isOverDue6, _maintenanceCheck_isOverDue7, _maintenanceCheck_isOverDue8, _maintenanceCheck_isOverDue9;\n    const isOverdue = (maintenanceCheck === null || maintenanceCheck === void 0 ? void 0 : (_maintenanceCheck_isOverDue = maintenanceCheck.isOverDue) === null || _maintenanceCheck_isOverDue === void 0 ? void 0 : _maintenanceCheck_isOverDue.status) === \"High\";\n    // Get status text\n    let statusText = \"\";\n    if ((maintenanceCheck === null || maintenanceCheck === void 0 ? void 0 : (_maintenanceCheck_isOverDue1 = maintenanceCheck.isOverDue) === null || _maintenanceCheck_isOverDue1 === void 0 ? void 0 : _maintenanceCheck_isOverDue1.status) && [\n        \"High\",\n        \"Medium\",\n        \"Low\"\n    ].includes(maintenanceCheck.isOverDue.status)) {\n        var _maintenanceCheck_isOverDue10;\n        statusText = maintenanceCheck === null || maintenanceCheck === void 0 ? void 0 : (_maintenanceCheck_isOverDue10 = maintenanceCheck.isOverDue) === null || _maintenanceCheck_isOverDue10 === void 0 ? void 0 : _maintenanceCheck_isOverDue10.days;\n    } else if ((maintenanceCheck === null || maintenanceCheck === void 0 ? void 0 : (_maintenanceCheck_isOverDue2 = maintenanceCheck.isOverDue) === null || _maintenanceCheck_isOverDue2 === void 0 ? void 0 : _maintenanceCheck_isOverDue2.status) === \"Completed\" && (maintenanceCheck === null || maintenanceCheck === void 0 ? void 0 : (_maintenanceCheck_isOverDue3 = maintenanceCheck.isOverDue) === null || _maintenanceCheck_isOverDue3 === void 0 ? void 0 : _maintenanceCheck_isOverDue3.days) === \"Save As Draft\") {\n        var _maintenanceCheck_isOverDue11;\n        statusText = maintenanceCheck === null || maintenanceCheck === void 0 ? void 0 : (_maintenanceCheck_isOverDue11 = maintenanceCheck.isOverDue) === null || _maintenanceCheck_isOverDue11 === void 0 ? void 0 : _maintenanceCheck_isOverDue11.days;\n    } else if ((maintenanceCheck === null || maintenanceCheck === void 0 ? void 0 : (_maintenanceCheck_isOverDue4 = maintenanceCheck.isOverDue) === null || _maintenanceCheck_isOverDue4 === void 0 ? void 0 : _maintenanceCheck_isOverDue4.status) === \"Upcoming\") {\n        var _maintenanceCheck_isOverDue12;\n        statusText = maintenanceCheck === null || maintenanceCheck === void 0 ? void 0 : (_maintenanceCheck_isOverDue12 = maintenanceCheck.isOverDue) === null || _maintenanceCheck_isOverDue12 === void 0 ? void 0 : _maintenanceCheck_isOverDue12.days;\n    } else if ((maintenanceCheck === null || maintenanceCheck === void 0 ? void 0 : (_maintenanceCheck_isOverDue5 = maintenanceCheck.isOverDue) === null || _maintenanceCheck_isOverDue5 === void 0 ? void 0 : _maintenanceCheck_isOverDue5.status) === \"Completed\" && lodash_isEmpty__WEBPACK_IMPORTED_MODULE_6___default()(maintenanceCheck === null || maintenanceCheck === void 0 ? void 0 : (_maintenanceCheck_isOverDue6 = maintenanceCheck.isOverDue) === null || _maintenanceCheck_isOverDue6 === void 0 ? void 0 : _maintenanceCheck_isOverDue6.days)) {\n        var _maintenanceCheck_isOverDue13;\n        statusText = maintenanceCheck === null || maintenanceCheck === void 0 ? void 0 : (_maintenanceCheck_isOverDue13 = maintenanceCheck.isOverDue) === null || _maintenanceCheck_isOverDue13 === void 0 ? void 0 : _maintenanceCheck_isOverDue13.status;\n    } else if ((maintenanceCheck === null || maintenanceCheck === void 0 ? void 0 : (_maintenanceCheck_isOverDue7 = maintenanceCheck.isOverDue) === null || _maintenanceCheck_isOverDue7 === void 0 ? void 0 : _maintenanceCheck_isOverDue7.status) === \"Completed\" && !lodash_isEmpty__WEBPACK_IMPORTED_MODULE_6___default()(maintenanceCheck === null || maintenanceCheck === void 0 ? void 0 : (_maintenanceCheck_isOverDue8 = maintenanceCheck.isOverDue) === null || _maintenanceCheck_isOverDue8 === void 0 ? void 0 : _maintenanceCheck_isOverDue8.days) && (maintenanceCheck === null || maintenanceCheck === void 0 ? void 0 : (_maintenanceCheck_isOverDue9 = maintenanceCheck.isOverDue) === null || _maintenanceCheck_isOverDue9 === void 0 ? void 0 : _maintenanceCheck_isOverDue9.days) !== \"Save As Draft\") {\n        var _maintenanceCheck_isOverDue14;\n        statusText = maintenanceCheck === null || maintenanceCheck === void 0 ? void 0 : (_maintenanceCheck_isOverDue14 = maintenanceCheck.isOverDue) === null || _maintenanceCheck_isOverDue14 === void 0 ? void 0 : _maintenanceCheck_isOverDue14.days;\n    }\n    // Only apply styling to overdue items, others are plain text\n    if (isOverdue) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n            className: \"alert w-fit inline-block rounded-md text-nowrap mr-2.5 text-sm xs:text-base !px-1.5 !py-0.5 xs:px-3 xs:py-1\",\n            children: statusText\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n            lineNumber: 169,\n            columnNumber: 13\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n        className: \"text-nowrap text-sm xs:text-base !px-1.5 !py-0.5 xs:px-3 xs:py-1\",\n        children: statusText\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n        lineNumber: 175,\n        columnNumber: 12\n    }, undefined);\n};\n_c = StatusBadge;\n// Reusable MaintenanceTable component that accepts props\nfunction MaintenanceTable(param) {\n    let { maintenanceChecks, vessels, crewInfo, showVessel = false } = param;\n    _s();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_7__.usePathname)();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_7__.useSearchParams)();\n    const { vesselIconData, getVesselWithIcon } = (0,_app_lib_vessel_icon_helper__WEBPACK_IMPORTED_MODULE_19__.useVesselIconData)();\n    const columns = (0,_components_filteredTable__WEBPACK_IMPORTED_MODULE_12__.createColumns)([\n        {\n            accessorKey: \"title\",\n            header: (param)=>{\n                let { column } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_13__.DataTableSortHeader, {\n                    column: column,\n                    title: \"Title\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                    lineNumber: 197,\n                    columnNumber: 17\n                }, this);\n            },\n            cell: (param)=>{\n                let { row } = param;\n                var _maintenanceCheck_isOverDue, _maintenanceCheck_basicComponent, _maintenanceCheck_assignedTo;\n                const maintenanceCheck = row.original;\n                const overDueStatus = (_maintenanceCheck_isOverDue = maintenanceCheck.isOverDue) === null || _maintenanceCheck_isOverDue === void 0 ? void 0 : _maintenanceCheck_isOverDue.status;\n                var _maintenanceCheck_name;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                href: \"/maintenance?taskID=\".concat(maintenanceCheck.id, \"&redirect_to=\").concat(pathname, \"?\").concat(searchParams.toString()),\n                                className: \"\".concat(overDueStatus === \"High\" ? \"text-cinnabar-500 hover:text-cinnabar-700\" : overDueStatus === \"Upcoming\" ? \"text-fire-bush-500 hover:text-fire-bush-700\" : \"hover:text-curious-blue-400\"),\n                                children: (_maintenanceCheck_name = maintenanceCheck.name) !== null && _maintenanceCheck_name !== void 0 ? _maintenanceCheck_name : \"Task #\".concat(maintenanceCheck.id, \" (No Name) - \").concat(dayjs__WEBPACK_IMPORTED_MODULE_5___default()(maintenanceCheck.created).format(\"DD/MM/YYYY\"))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                lineNumber: 205,\n                                columnNumber: 29\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                            lineNumber: 204,\n                            columnNumber: 25\n                        }, this),\n                        !showVessel && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:hidden\",\n                            children: ((_maintenanceCheck_basicComponent = maintenanceCheck.basicComponent) === null || _maintenanceCheck_basicComponent === void 0 ? void 0 : _maintenanceCheck_basicComponent.id) > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                href: \"/vessel/info?id=\".concat(maintenanceCheck.basicComponent.id),\n                                className: \"text-sm text-muted-foreground hover:underline\",\n                                children: maintenanceCheck.basicComponent.title\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                lineNumber: 225,\n                                columnNumber: 37\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                            lineNumber: 223,\n                            columnNumber: 29\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"md:hidden space-y-2\",\n                            children: [\n                                ((_maintenanceCheck_assignedTo = maintenanceCheck.assignedTo) === null || _maintenanceCheck_assignedTo === void 0 ? void 0 : _maintenanceCheck_assignedTo.id) > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-muted-foreground\",\n                                            children: [\n                                                \"Assigned to:\",\n                                                \" \"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                            lineNumber: 238,\n                                            columnNumber: 37\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                            href: \"/crew/info?id=\".concat(maintenanceCheck.assignedTo.id),\n                                            className: \"hover:underline\",\n                                            children: maintenanceCheck.assignedTo.name\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                            lineNumber: 241,\n                                            columnNumber: 37\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                    lineNumber: 237,\n                                    columnNumber: 33\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatusBadge, {\n                                        maintenanceCheck: maintenanceCheck\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                        lineNumber: 249,\n                                        columnNumber: 33\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                    lineNumber: 248,\n                                    columnNumber: 29\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                            lineNumber: 235,\n                            columnNumber: 25\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                    lineNumber: 203,\n                    columnNumber: 21\n                }, this);\n            },\n            sortingFn: (rowA, rowB)=>{\n                var _rowA_original, _rowB_original;\n                const valueA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : _rowA_original.name) || \"\";\n                const valueB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : _rowB_original.name) || \"\";\n                return valueA.localeCompare(valueB);\n            }\n        },\n        ...showVessel ? [] : [\n            {\n                accessorKey: \"location\",\n                header: (param)=>{\n                    let { column } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_13__.DataTableSortHeader, {\n                        column: column,\n                        title: \"Location\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                        lineNumber: 272,\n                        columnNumber: 27\n                    }, this);\n                },\n                cellAlignment: \"left\",\n                cell: (param)=>{\n                    let { row } = param;\n                    var _maintenanceCheck_basicComponent, _maintenanceCheck_basicComponent1;\n                    const maintenanceCheck = row.original;\n                    const vesselDetails = getVesselDetails(((_maintenanceCheck_basicComponent = maintenanceCheck.basicComponent) === null || _maintenanceCheck_basicComponent === void 0 ? void 0 : _maintenanceCheck_basicComponent.id) || 0, vessels);\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"hidden lg:block\",\n                        children: ((_maintenanceCheck_basicComponent1 = maintenanceCheck.basicComponent) === null || _maintenanceCheck_basicComponent1 === void 0 ? void 0 : _maintenanceCheck_basicComponent1.id) > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2.5\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_16__.Avatar, {\n                                    size: \"sm\",\n                                    variant: \"secondary\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_16__.AvatarFallback, {\n                                        className: \"text-xs\",\n                                        children: getVesselInitials((vesselDetails === null || vesselDetails === void 0 ? void 0 : vesselDetails.title) || maintenanceCheck.basicComponent.title || undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                        lineNumber: 290,\n                                        columnNumber: 47\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                    lineNumber: 289,\n                                    columnNumber: 43\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    href: \"/vessel/info?id=\".concat(maintenanceCheck.basicComponent.id),\n                                    className: \"hover:underline\",\n                                    children: maintenanceCheck.basicComponent.title\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                    lineNumber: 300,\n                                    columnNumber: 43\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                            lineNumber: 288,\n                            columnNumber: 39\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                        lineNumber: 286,\n                        columnNumber: 31\n                    }, this);\n                },\n                sortingFn: (rowA, rowB)=>{\n                    var _rowA_original_basicComponent, _rowA_original, _rowB_original_basicComponent, _rowB_original;\n                    const valueA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : (_rowA_original_basicComponent = _rowA_original.basicComponent) === null || _rowA_original_basicComponent === void 0 ? void 0 : _rowA_original_basicComponent.title) || \"\";\n                    const valueB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : (_rowB_original_basicComponent = _rowB_original.basicComponent) === null || _rowB_original_basicComponent === void 0 ? void 0 : _rowB_original_basicComponent.title) || \"\";\n                    return valueA.localeCompare(valueB);\n                }\n            }\n        ],\n        {\n            accessorKey: \"assigned\",\n            header: (param)=>{\n                let { column } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_13__.DataTableSortHeader, {\n                    column: column,\n                    title: \"Assigned to\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                    lineNumber: 328,\n                    columnNumber: 17\n                }, this);\n            },\n            cellAlignment: \"left\",\n            breakpoint: \"tablet-sm\",\n            cell: (param)=>{\n                let { row } = param;\n                var _maintenanceCheck_assignedTo, _maintenanceCheck_assignedTo1, _maintenanceCheck_assignedTo2;\n                const maintenanceCheck = row.original;\n                const crewDetails = getCrewDetails(((_maintenanceCheck_assignedTo = maintenanceCheck.assignedTo) === null || _maintenanceCheck_assignedTo === void 0 ? void 0 : _maintenanceCheck_assignedTo.id) || 0, crewInfo);\n                // Temporary debug log to verify the fix\n                if ((_maintenanceCheck_assignedTo1 = maintenanceCheck.assignedTo) === null || _maintenanceCheck_assignedTo1 === void 0 ? void 0 : _maintenanceCheck_assignedTo1.id) {\n                    console.log(\"✅ Crew lookup:\", {\n                        assignedToId: maintenanceCheck.assignedTo.id,\n                        crewFound: !!crewDetails,\n                        crewName: crewDetails ? \"\".concat(crewDetails.firstName, \" \").concat(crewDetails.surname) : \"Not found\"\n                    });\n                }\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"hidden md:block\",\n                    children: ((_maintenanceCheck_assignedTo2 = maintenanceCheck.assignedTo) === null || _maintenanceCheck_assignedTo2 === void 0 ? void 0 : _maintenanceCheck_assignedTo2.id) > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2.5\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_16__.Avatar, {\n                                className: \"h-8 w-8\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_16__.AvatarFallback, {\n                                    className: \"text-xs\",\n                                    children: getCrewInitials(crewDetails === null || crewDetails === void 0 ? void 0 : crewDetails.firstName, crewDetails === null || crewDetails === void 0 ? void 0 : crewDetails.surname)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                    lineNumber: 355,\n                                    columnNumber: 37\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                lineNumber: 354,\n                                columnNumber: 33\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                href: \"/crew/info?id=\".concat(maintenanceCheck.assignedTo.id),\n                                className: \"hover:underline hidden lg:block\",\n                                children: maintenanceCheck.assignedTo.name\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                lineNumber: 362,\n                                columnNumber: 33\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                        lineNumber: 353,\n                        columnNumber: 29\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                    lineNumber: 351,\n                    columnNumber: 21\n                }, this);\n            },\n            sortingFn: (rowA, rowB)=>{\n                var _rowA_original_assignedTo, _rowA_original, _rowB_original_assignedTo, _rowB_original;\n                const valueA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : (_rowA_original_assignedTo = _rowA_original.assignedTo) === null || _rowA_original_assignedTo === void 0 ? void 0 : _rowA_original_assignedTo.name) || \"\";\n                const valueB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : (_rowB_original_assignedTo = _rowB_original.assignedTo) === null || _rowB_original_assignedTo === void 0 ? void 0 : _rowB_original_assignedTo.name) || \"\";\n                return valueA.localeCompare(valueB);\n            }\n        },\n        {\n            accessorKey: \"inventory\",\n            header: \"Inventory item\",\n            cellAlignment: \"left\",\n            breakpoint: \"tablet-lg\",\n            cell: (param)=>{\n                let { row } = param;\n                var _maintenanceCheck_inventory;\n                const maintenanceCheck = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"hidden md:block\",\n                    children: ((_maintenanceCheck_inventory = maintenanceCheck.inventory) === null || _maintenanceCheck_inventory === void 0 ? void 0 : _maintenanceCheck_inventory.id) > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        href: \"/inventory/view?id=\".concat(maintenanceCheck.inventory.id),\n                        className: \"hover:underline\",\n                        children: maintenanceCheck.inventory.item\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                        lineNumber: 391,\n                        columnNumber: 29\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                    lineNumber: 389,\n                    columnNumber: 21\n                }, this);\n            }\n        },\n        {\n            accessorKey: \"status\",\n            header: (param)=>{\n                let { column } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_13__.DataTableSortHeader, {\n                    column: column,\n                    title: \"Status\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                    lineNumber: 404,\n                    columnNumber: 17\n                }, this);\n            },\n            cellAlignment: \"right\",\n            cell: (param)=>{\n                let { row } = param;\n                const maintenanceCheck = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"hidden md:block\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatusBadge, {\n                        maintenanceCheck: maintenanceCheck\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                        lineNumber: 411,\n                        columnNumber: 25\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                    lineNumber: 410,\n                    columnNumber: 21\n                }, this);\n            },\n            sortingFn: (rowA, rowB)=>{\n                var _rowA_original_isOverDue, _rowA_original, _rowB_original_isOverDue, _rowB_original;\n                const valueA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : (_rowA_original_isOverDue = _rowA_original.isOverDue) === null || _rowA_original_isOverDue === void 0 ? void 0 : _rowA_original_isOverDue.days) || \"\";\n                const valueB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : (_rowB_original_isOverDue = _rowB_original.isOverDue) === null || _rowB_original_isOverDue === void 0 ? void 0 : _rowB_original_isOverDue.days) || \"\";\n                return valueA.localeCompare(valueB);\n            }\n        }\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_filteredTable__WEBPACK_IMPORTED_MODULE_12__.DataTable, {\n        columns: columns,\n        data: maintenanceChecks,\n        pageSize: 20,\n        showToolbar: false\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n        lineNumber: 427,\n        columnNumber: 9\n    }, this);\n}\n_s(MaintenanceTable, \"Zp7KzlBBuyYf0TXkH/rj/yT9nuA=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_7__.usePathname,\n        next_navigation__WEBPACK_IMPORTED_MODULE_7__.useSearchParams,\n        _app_lib_vessel_icon_helper__WEBPACK_IMPORTED_MODULE_19__.useVesselIconData\n    ];\n});\n_c1 = MaintenanceTable;\nfunction TaskList() {\n    _s1();\n    var _s = $RefreshSig$();\n    const [maintenanceChecks, setMaintenanceChecks] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    const [filteredMaintenanceChecks, setFilteredMaintenanceChecks] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    const [vessels, setVessels] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    const [crewInfo, setCrewInfo] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    const [filter, setFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [keywordFilter, setKeywordFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [permissions, setPermissions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [edit_task, setEdit_task] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_7__.usePathname)();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_7__.useSearchParams)();\n    const { getVesselWithIcon } = (0,_app_lib_vessel_icon_helper__WEBPACK_IMPORTED_MODULE_19__.useVesselIconData)();\n    const bp = (0,_components_hooks_useBreakpoints__WEBPACK_IMPORTED_MODULE_20__.useBreakpoints)();\n    const init_permissions = ()=>{\n        if (permissions) {\n            if ((0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_8__.hasPermission)(\"EDIT_TASK\", permissions)) {\n                setEdit_task(true);\n            } else {\n                setEdit_task(false);\n            }\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setPermissions(_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_8__.getPermissions);\n        init_permissions();\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (permissions) {\n            init_permissions();\n        }\n    }, [\n        permissions\n    ]);\n    const [queryMaintenanceChecks] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_26__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_2__.GET_MAINTENANCE_CHECK_LIST, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            const data = response.readComponentMaintenanceCheckList[0].list;\n            if (data) {\n                handleSetMaintenanceChecks(data);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"queryMaintenanceChecks error\", error);\n        }\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isLoading) {\n            loadMaintenanceChecks();\n            setIsLoading(false);\n        }\n    }, [\n        isLoading\n    ]);\n    const loadMaintenanceChecks = async ()=>{\n        await queryMaintenanceChecks({\n            variables: {\n                inventoryID: 0,\n                vesselID: 0\n            }\n        });\n    };\n    const handleSetVessels = (vessels)=>{\n        const activeVessels = vessels.filter((vessel)=>!vessel.archived);\n        const appendedData = activeVessels.map((item)=>({\n                ...item\n            }));\n        appendedData.push({\n            title: \"Other\",\n            id: 0\n        });\n        setVessels(appendedData);\n    };\n    const getVesselList = function(handleSetVessels) {\n        let offline = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : false;\n        _s();\n        const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n        const vesselModel = new _app_offline_models_vessel__WEBPACK_IMPORTED_MODULE_24__[\"default\"]();\n        (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n            if (isLoading) {\n                loadVessels();\n                setIsLoading(false);\n            }\n        }, [\n            isLoading\n        ]);\n        const [queryVessels] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_26__.useLazyQuery)(_queries__WEBPACK_IMPORTED_MODULE_23__.ReadVessels, {\n            fetchPolicy: \"cache-and-network\",\n            onCompleted: (queryVesselResponse)=>{\n                if (queryVesselResponse.readVessels.nodes) {\n                    handleSetVessels(queryVesselResponse.readVessels.nodes);\n                }\n            },\n            onError: (error)=>{\n                console.error(\"queryVessels error\", error);\n            }\n        });\n        const loadVessels = async ()=>{\n            if (offline) {\n                const response = await vesselModel.getAll();\n                handleSetVessels(response);\n            } else {\n                await queryVessels({\n                    variables: {\n                        limit: 200,\n                        offset: 0\n                    }\n                });\n            }\n        };\n    };\n    _s(getVesselList, \"pQOK42e7v9ItR64U9CP/qx2cQME=\", false, function() {\n        return [\n            _apollo_client__WEBPACK_IMPORTED_MODULE_26__.useLazyQuery\n        ];\n    });\n    getVesselList(handleSetVessels);\n    const handleSetMaintenanceChecks = (tasks)=>{\n        setMaintenanceChecks(tasks);\n        setFilteredMaintenanceChecks(tasks);\n        const appendedData = Array.from(new Set(tasks.filter((item)=>item.assignedTo.id > 0).map((item)=>item.assignedTo.id)));\n        loadCrewMemberInfo(appendedData);\n    };\n    const [queryCrewMemberInfo] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_26__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_2__.GET_CREW_BY_IDS, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            const data = response.readSeaLogsMembers.nodes;\n            if (data) {\n                setCrewInfo(data);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"queryCrewMemberInfo error\", error);\n        }\n    });\n    const loadCrewMemberInfo = async (crewId)=>{\n        await queryCrewMemberInfo({\n            variables: {\n                crewMemberIDs: crewId.length > 0 ? crewId : [\n                    0\n                ]\n            }\n        });\n    };\n    const handleFilterOnChange = (param)=>{\n        let { type, data } = param;\n        const searchFilter = {\n            ...filter\n        };\n        let filteredTasks = maintenanceChecks || [];\n        // Vessel filter\n        if (type === \"vessel\") {\n            if (Array.isArray(data) && data.length > 0) {\n                searchFilter.basicComponentID = {\n                    in: data.map((item)=>+item.value)\n                };\n            } else if (data && !Array.isArray(data)) {\n                searchFilter.basicComponentID = {\n                    eq: +data.value\n                };\n            } else {\n                delete searchFilter.basicComponentID;\n            }\n        }\n        // Status filter\n        if (type === \"status\") {\n            if (Array.isArray(data) && data.length > 0) {\n                searchFilter.status = {\n                    in: data.map((item)=>item.value)\n                };\n            } else if (data && !Array.isArray(data)) {\n                searchFilter.status = {\n                    eq: data.value\n                };\n            } else {\n                delete searchFilter.status;\n            }\n        }\n        // Assigned member filter\n        if (type === \"member\") {\n            if (Array.isArray(data) && data.length > 0) {\n                searchFilter.assignedToID = {\n                    in: data.map((item)=>+item.value)\n                };\n            } else if (data && !Array.isArray(data)) {\n                searchFilter.assignedToID = {\n                    eq: +data.value\n                };\n            } else {\n                delete searchFilter.assignedToID;\n            }\n        }\n        // Date range\n        if (type === \"dateRange\") {\n            if ((data === null || data === void 0 ? void 0 : data.startDate) && (data === null || data === void 0 ? void 0 : data.endDate)) {\n                searchFilter.expires = {\n                    gte: data.startDate,\n                    lte: data.endDate\n                };\n            } else {\n                delete searchFilter.expires;\n            }\n        }\n        // Category\n        if (type === \"category\") {\n            if (Array.isArray(data) && data.length > 0) {\n                searchFilter.maintenanceCategoryID = {\n                    in: data.map((item)=>+item.value)\n                };\n            } else if (data && !Array.isArray(data)) {\n                searchFilter.maintenanceCategoryID = {\n                    eq: +data.value\n                };\n            } else {\n                delete searchFilter.maintenanceCategoryID;\n            }\n        }\n        // Recurring filter - handle client-side filtering\n        let recurringFilter = null;\n        if (type === \"recurring\") {\n            if (data && !Array.isArray(data)) {\n                recurringFilter = data.value;\n            }\n        }\n        // Keyword filter\n        let keyFilter = keywordFilter;\n        if (type === \"keyword\" || keyFilter && keyFilter.length > 0) {\n            var _data_value;\n            const keyword = data === null || data === void 0 ? void 0 : (_data_value = data.value) === null || _data_value === void 0 ? void 0 : _data_value.trim().toLowerCase();\n            if (keyword && keyword.length > 0) {\n                filteredTasks = filteredTasks.filter((maintenanceCheck)=>[\n                        maintenanceCheck.name,\n                        maintenanceCheck.comments,\n                        maintenanceCheck.workOrderNumber\n                    ].some((field)=>field === null || field === void 0 ? void 0 : field.toLowerCase().includes(keyword)));\n                keyFilter = data.value;\n            } else {\n                keyFilter = null;\n            }\n        }\n        // Filtering based on current searchFilter\n        // Filter by vessel (basicComponentID)\n        if (searchFilter.basicComponentID) {\n            const ids = searchFilter.basicComponentID.in || [\n                searchFilter.basicComponentID.eq\n            ];\n            filteredTasks = filteredTasks.filter((mc)=>{\n                var _mc_basicComponent;\n                return ids.includes((_mc_basicComponent = mc.basicComponent) === null || _mc_basicComponent === void 0 ? void 0 : _mc_basicComponent.id);\n            });\n        }\n        // Filter by status\n        if (searchFilter.status) {\n            const statuses = searchFilter.status.in || [\n                searchFilter.status.eq\n            ];\n            filteredTasks = filteredTasks.filter((mc)=>statuses.includes(mc.status));\n        }\n        // Filter by assignedToID\n        if (searchFilter.assignedToID) {\n            const ids = searchFilter.assignedToID.in || [\n                searchFilter.assignedToID.eq\n            ];\n            filteredTasks = filteredTasks.filter((mc)=>{\n                var _mc_assignedTo;\n                return ids.includes((_mc_assignedTo = mc.assignedTo) === null || _mc_assignedTo === void 0 ? void 0 : _mc_assignedTo.id);\n            });\n        }\n        // Filter by category\n        if (searchFilter.maintenanceCategoryID) {\n            const ids = searchFilter.maintenanceCategoryID.in || [\n                searchFilter.maintenanceCategoryID.eq\n            ];\n            filteredTasks = filteredTasks.filter((mc)=>ids.includes(mc.maintenanceCategoryID));\n        }\n        // Filter by date range\n        if (searchFilter.expires && searchFilter.expires.gte && searchFilter.expires.lte) {\n            filteredTasks = filteredTasks.filter((mc)=>dayjs__WEBPACK_IMPORTED_MODULE_5___default()(mc.startDate).isAfter(dayjs__WEBPACK_IMPORTED_MODULE_5___default()(searchFilter.expires.gte)) && dayjs__WEBPACK_IMPORTED_MODULE_5___default()(mc.startDate).isBefore(dayjs__WEBPACK_IMPORTED_MODULE_5___default()(searchFilter.expires.lte)));\n        }\n        // Filter by recurring status\n        if (recurringFilter) {\n            if (recurringFilter === \"recurring\") {\n                // Recurring tasks have recurringID > 0\n                filteredTasks = filteredTasks.filter((mc)=>mc.recurringID > 0);\n            } else if (recurringFilter === \"one-off\") {\n                // One-off tasks have recurringID = 0 or null\n                filteredTasks = filteredTasks.filter((mc)=>!mc.recurringID || mc.recurringID === 0);\n            }\n        }\n        // Set updated filters\n        setFilter(searchFilter);\n        setKeywordFilter(keyFilter);\n        setFilteredMaintenanceChecks(filteredTasks);\n    // Optionally call API: loadMaintenanceChecks(searchFilter, keyFilter);\n    };\n    const downloadCsv = ()=>{\n        if (!maintenanceChecks || !vessels) {\n            return;\n        }\n        const csvEntries = [\n            [\n                \"task\",\n                \"location\",\n                \"assigned to\",\n                \"due\"\n            ]\n        ];\n        maintenanceChecks.filter((maintenanceCheck)=>maintenanceCheck.status !== \"Save_As_Draft\").forEach((maintenanceCheck)=>{\n            var _maintenanceCheck_assignedTo, _maintenanceCheck_assignedTo1;\n            const crewDetails = getCrewDetails(((_maintenanceCheck_assignedTo = maintenanceCheck.assignedTo) === null || _maintenanceCheck_assignedTo === void 0 ? void 0 : _maintenanceCheck_assignedTo.id) || 0, crewInfo);\n            const assignedToName = crewDetails ? \"\".concat(crewDetails.firstName, \" \").concat(crewDetails.surname) : ((_maintenanceCheck_assignedTo1 = maintenanceCheck.assignedTo) === null || _maintenanceCheck_assignedTo1 === void 0 ? void 0 : _maintenanceCheck_assignedTo1.name) || \"\";\n            csvEntries.push([\n                maintenanceCheck.name,\n                (vessels === null || vessels === void 0 ? void 0 : vessels.filter((vessel)=>{\n                    var _maintenanceCheck_basicComponent;\n                    return (vessel === null || vessel === void 0 ? void 0 : vessel.id) == ((_maintenanceCheck_basicComponent = maintenanceCheck.basicComponent) === null || _maintenanceCheck_basicComponent === void 0 ? void 0 : _maintenanceCheck_basicComponent.id);\n                }).map((vessel)=>vessel.title).join(\", \")) || \"\",\n                assignedToName,\n                (0,_reporting_maintenance_status_activity_report__WEBPACK_IMPORTED_MODULE_9__.dueStatusLabel)(maintenanceCheck.isOverDue)\n            ]);\n        });\n        (0,_app_helpers_csvHelper__WEBPACK_IMPORTED_MODULE_10__.exportCsv)(csvEntries);\n    };\n    const downloadPdf = ()=>{\n        if (!maintenanceChecks || !vessels) {\n            return;\n        }\n        const headers = [\n            [\n                \"Task Name\",\n                \"Location\",\n                \"Assigned To\",\n                \"Due\"\n            ]\n        ];\n        const body = maintenanceChecks.filter((maintenanceCheck)=>maintenanceCheck.status !== \"Save_As_Draft\").map((maintenanceCheck)=>{\n            var _maintenanceCheck_assignedTo, _maintenanceCheck_assignedTo1;\n            const crewDetails = getCrewDetails(((_maintenanceCheck_assignedTo = maintenanceCheck.assignedTo) === null || _maintenanceCheck_assignedTo === void 0 ? void 0 : _maintenanceCheck_assignedTo.id) || 0, crewInfo);\n            const assignedToName = crewDetails ? \"\".concat(crewDetails.firstName, \" \").concat(crewDetails.surname) : ((_maintenanceCheck_assignedTo1 = maintenanceCheck.assignedTo) === null || _maintenanceCheck_assignedTo1 === void 0 ? void 0 : _maintenanceCheck_assignedTo1.name) || \"\";\n            return [\n                maintenanceCheck.name,\n                (vessels === null || vessels === void 0 ? void 0 : vessels.filter((vessel)=>{\n                    var _maintenanceCheck_basicComponent;\n                    return (vessel === null || vessel === void 0 ? void 0 : vessel.id) == ((_maintenanceCheck_basicComponent = maintenanceCheck.basicComponent) === null || _maintenanceCheck_basicComponent === void 0 ? void 0 : _maintenanceCheck_basicComponent.id);\n                }).map((vessel)=>vessel.title).join(\", \")) || \"\",\n                assignedToName,\n                (0,_reporting_maintenance_status_activity_report__WEBPACK_IMPORTED_MODULE_9__.dueStatusLabel)(maintenanceCheck.isOverDue)\n            ];\n        });\n        (0,_app_helpers_pdfHelper__WEBPACK_IMPORTED_MODULE_11__.exportPdfTable)({\n            headers,\n            body\n        });\n    };\n    const createMaintenanceColumns = (crewInfo, getVesselWithIcon)=>(0,_components_filteredTable__WEBPACK_IMPORTED_MODULE_12__.createColumns)([\n            {\n                accessorKey: \"title\",\n                header: (param)=>{\n                    let { column } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_13__.DataTableSortHeader, {\n                        column: column,\n                        title: \"Title\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                        lineNumber: 844,\n                        columnNumber: 21\n                    }, this);\n                },\n                cell: (param)=>{\n                    let { row } = param;\n                    var _maintenanceCheck_assignedTo, _maintenanceCheck_isOverDue, _maintenanceCheck_isOverDue1, _maintenanceCheck_inventory, _maintenanceCheck_assignedTo1, _maintenanceCheck_basicComponent;\n                    const maintenanceCheck = row.original;\n                    const crewDetails = getCrewDetails(((_maintenanceCheck_assignedTo = maintenanceCheck.assignedTo) === null || _maintenanceCheck_assignedTo === void 0 ? void 0 : _maintenanceCheck_assignedTo.id) || 0, crewInfo);\n                    const overDueStatus = (_maintenanceCheck_isOverDue = maintenanceCheck.isOverDue) === null || _maintenanceCheck_isOverDue === void 0 ? void 0 : _maintenanceCheck_isOverDue.status;\n                    const overDueDays = (_maintenanceCheck_isOverDue1 = maintenanceCheck.isOverDue) === null || _maintenanceCheck_isOverDue1 === void 0 ? void 0 : _maintenanceCheck_isOverDue1.day;\n                    var _maintenanceCheck_name, _maintenanceCheck_name1;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_21__.Card, {\n                                className: \"xs:hidden space-y-3 p-2.5 min-h-20 w-full shadow-none rounded-none bg-transparent\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_21__.CardContent, {\n                                    className: \"p-0 space-y-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                            href: \"/maintenance?taskID=\".concat(maintenanceCheck.id, \"&redirect_to=\").concat(pathname, \"?\").concat(searchParams.toString()),\n                                            className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_25__.cn)(\"text-base\", overDueStatus === \"High\" ? \"text-cinnabar-500 hover:text-cinnabar-700\" : overDueStatus === \"Upcoming\" ? \"text-fire-bush-500 hover:text-fire-bush-700\" : \"hover:text-curious-blue-400\"),\n                                            children: [\n                                                (_maintenanceCheck_name = maintenanceCheck.name) !== null && _maintenanceCheck_name !== void 0 ? _maintenanceCheck_name : \"Task #\".concat(maintenanceCheck.id, \" (No Name) - \").concat(dayjs__WEBPACK_IMPORTED_MODULE_5___default()(maintenanceCheck.created).format(\"DD/MM/YYYY\")),\n                                                \" \"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                            lineNumber: 862,\n                                            columnNumber: 37\n                                        }, this),\n                                        ((_maintenanceCheck_inventory = maintenanceCheck.inventory) === null || _maintenanceCheck_inventory === void 0 ? void 0 : _maintenanceCheck_inventory.id) > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-col\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xs text-muted-foreground\",\n                                                    children: \"Inventory item\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                                    lineNumber: 881,\n                                                    columnNumber: 45\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                    href: \"/inventory/view?id=\".concat(maintenanceCheck.inventory.id),\n                                                    className: \"hover:underline text-sm\",\n                                                    children: maintenanceCheck.inventory.item\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                                    lineNumber: 884,\n                                                    columnNumber: 45\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                            lineNumber: 880,\n                                            columnNumber: 41\n                                        }, this),\n                                        ((_maintenanceCheck_assignedTo1 = maintenanceCheck.assignedTo) === null || _maintenanceCheck_assignedTo1 === void 0 ? void 0 : _maintenanceCheck_assignedTo1.id) > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_16__.Avatar, {\n                                            variant: \"secondary\",\n                                            className: \"h-8 w-8\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_16__.AvatarFallback, {\n                                                className: \"text-xs\",\n                                                children: getCrewInitials(crewDetails === null || crewDetails === void 0 ? void 0 : crewDetails.firstName, crewDetails === null || crewDetails === void 0 ? void 0 : crewDetails.surname)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                                lineNumber: 900,\n                                                columnNumber: 45\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                            lineNumber: 897,\n                                            columnNumber: 41\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                    lineNumber: 860,\n                                    columnNumber: 33\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                lineNumber: 859,\n                                columnNumber: 29\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"hidden xs:block\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center flex-nowrap gap-2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                            href: \"/maintenance?taskID=\".concat(maintenanceCheck.id, \"&redirect_to=\").concat(pathname, \"?\").concat(searchParams.toString()),\n                                            className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_25__.cn)(\"text-base\", overDueStatus === \"High\" ? \"text-cinnabar-500 hover:text-cinnabar-700\" : overDueStatus === \"Upcoming\" ? \"text-fire-bush-500 hover:text-fire-bush-700\" : \"hover:text-curious-blue-400\"),\n                                            children: (_maintenanceCheck_name1 = maintenanceCheck.name) !== null && _maintenanceCheck_name1 !== void 0 ? _maintenanceCheck_name1 : \"Task #\".concat(maintenanceCheck.id, \" (No Name) - \").concat(dayjs__WEBPACK_IMPORTED_MODULE_5___default()(maintenanceCheck.created).format(\"DD/MM/YYYY\"))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                            lineNumber: 914,\n                                            columnNumber: 37\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                        lineNumber: 913,\n                                        columnNumber: 33\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"md:hidden\",\n                                        children: ((_maintenanceCheck_basicComponent = maintenanceCheck.basicComponent) === null || _maintenanceCheck_basicComponent === void 0 ? void 0 : _maintenanceCheck_basicComponent.id) > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                            href: \"/vessel/info?id=\".concat(maintenanceCheck.basicComponent.id),\n                                            className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_25__.cn)(\"text-base\", overDueStatus === \"High\" ? \"text-cinnabar-500 hover:text-cinnabar-700\" : overDueStatus === \"Upcoming\" ? \"text-fire-bush-500 hover:text-fire-bush-700\" : \"hover:text-curious-blue-400\"),\n                                            children: maintenanceCheck.basicComponent.title\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                            lineNumber: 934,\n                                            columnNumber: 41\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                        lineNumber: 931,\n                                        columnNumber: 33\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                lineNumber: 912,\n                                columnNumber: 29\n                            }, this)\n                        ]\n                    }, void 0, true);\n                },\n                sortingFn: (rowA, rowB)=>{\n                    var _rowA_original, _rowB_original;\n                    const valueA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : _rowA_original.name) || \"\";\n                    const valueB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : _rowB_original.name) || \"\";\n                    return valueA.localeCompare(valueB);\n                }\n            },\n            {\n                accessorKey: \"location\",\n                header: (param)=>{\n                    let { column } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_13__.DataTableSortHeader, {\n                        column: column,\n                        title: \"Location\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                        lineNumber: 968,\n                        columnNumber: 21\n                    }, this);\n                },\n                cellAlignment: \"left\",\n                breakpoint: \"laptop\",\n                cell: (param)=>{\n                    let { row } = param;\n                    var _maintenanceCheck_basicComponent, _maintenanceCheck_basicComponent1, _maintenanceCheck_basicComponent2;\n                    const maintenanceCheck = row.original;\n                    const vesselDetails = getVesselDetails(((_maintenanceCheck_basicComponent = maintenanceCheck.basicComponent) === null || _maintenanceCheck_basicComponent === void 0 ? void 0 : _maintenanceCheck_basicComponent.id) || 0, vessels);\n                    // Get vessel with icon data\n                    const vesselWithIcon = getVesselWithIcon(((_maintenanceCheck_basicComponent1 = maintenanceCheck.basicComponent) === null || _maintenanceCheck_basicComponent1 === void 0 ? void 0 : _maintenanceCheck_basicComponent1.id) || 0, vesselDetails);\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: ((_maintenanceCheck_basicComponent2 = maintenanceCheck.basicComponent) === null || _maintenanceCheck_basicComponent2 === void 0 ? void 0 : _maintenanceCheck_basicComponent2.id) > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2.5\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_17__.Tooltip, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_17__.TooltipTrigger, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"min-w-fit\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_vessels_vesel_icon__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                    vessel: vesselWithIcon\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                                    lineNumber: 992,\n                                                    columnNumber: 49\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                                lineNumber: 991,\n                                                columnNumber: 45\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                            lineNumber: 990,\n                                            columnNumber: 41\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_17__.TooltipContent, {\n                                            children: maintenanceCheck.basicComponent.title\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                            lineNumber: 997,\n                                            columnNumber: 41\n                                        }, this)\n                                    ]\n                                }, vesselDetails === null || vesselDetails === void 0 ? void 0 : vesselDetails.id, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                    lineNumber: 989,\n                                    columnNumber: 37\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    href: \"/vessel/info?id=\".concat(maintenanceCheck.basicComponent.id),\n                                    className: \"hover:underline\",\n                                    children: maintenanceCheck.basicComponent.title\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                    lineNumber: 1004,\n                                    columnNumber: 37\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                            lineNumber: 988,\n                            columnNumber: 33\n                        }, this)\n                    }, void 0, false);\n                },\n                sortingFn: (rowA, rowB)=>{\n                    var _rowA_original_basicComponent, _rowA_original, _rowB_original_basicComponent, _rowB_original;\n                    const valueA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : (_rowA_original_basicComponent = _rowA_original.basicComponent) === null || _rowA_original_basicComponent === void 0 ? void 0 : _rowA_original_basicComponent.title) || \"\";\n                    const valueB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : (_rowB_original_basicComponent = _rowB_original.basicComponent) === null || _rowB_original_basicComponent === void 0 ? void 0 : _rowB_original_basicComponent.title) || \"\";\n                    return valueA.localeCompare(valueB);\n                }\n            },\n            {\n                accessorKey: \"assigned\",\n                header: (param)=>{\n                    let { column } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_13__.DataTableSortHeader, {\n                        column: column,\n                        title: \"Assigned to\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                        lineNumber: 1026,\n                        columnNumber: 21\n                    }, this);\n                },\n                cellAlignment: \"left\",\n                breakpoint: \"tablet-md\",\n                cell: (param)=>{\n                    let { row } = param;\n                    var _maintenanceCheck_assignedTo, _maintenanceCheck_assignedTo1;\n                    const maintenanceCheck = row.original;\n                    const crewDetails = getCrewDetails(((_maintenanceCheck_assignedTo = maintenanceCheck.assignedTo) === null || _maintenanceCheck_assignedTo === void 0 ? void 0 : _maintenanceCheck_assignedTo.id) || 0, crewInfo);\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: ((_maintenanceCheck_assignedTo1 = maintenanceCheck.assignedTo) === null || _maintenanceCheck_assignedTo1 === void 0 ? void 0 : _maintenanceCheck_assignedTo1.id) > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2.5\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_16__.Avatar, {\n                                    variant: \"secondary\",\n                                    className: \"h-8 w-8\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_16__.AvatarFallback, {\n                                        className: \"text-xs\",\n                                        children: getCrewInitials(crewDetails === null || crewDetails === void 0 ? void 0 : crewDetails.firstName, crewDetails === null || crewDetails === void 0 ? void 0 : crewDetails.surname)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                        lineNumber: 1044,\n                                        columnNumber: 41\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                    lineNumber: 1041,\n                                    columnNumber: 37\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    href: \"/crew/info?id=\".concat(maintenanceCheck.assignedTo.id),\n                                    className: \"hover:underline hidden tablet-md:block\",\n                                    children: maintenanceCheck.assignedTo.name\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                    lineNumber: 1051,\n                                    columnNumber: 37\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                            lineNumber: 1040,\n                            columnNumber: 33\n                        }, this)\n                    }, void 0, false);\n                },\n                sortingFn: (rowA, rowB)=>{\n                    var _rowA_original_assignedTo, _rowA_original, _rowB_original_assignedTo, _rowB_original;\n                    const valueA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : (_rowA_original_assignedTo = _rowA_original.assignedTo) === null || _rowA_original_assignedTo === void 0 ? void 0 : _rowA_original_assignedTo.name) || \"\";\n                    const valueB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : (_rowB_original_assignedTo = _rowB_original.assignedTo) === null || _rowB_original_assignedTo === void 0 ? void 0 : _rowB_original_assignedTo.name) || \"\";\n                    return valueA.localeCompare(valueB);\n                }\n            },\n            {\n                accessorKey: \"inventory\",\n                header: \"Inventory item\",\n                cellAlignment: \"left\",\n                breakpoint: \"phablet\",\n                cell: (param)=>{\n                    let { row } = param;\n                    var _maintenanceCheck_inventory;\n                    const maintenanceCheck = row.original;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: ((_maintenanceCheck_inventory = maintenanceCheck.inventory) === null || _maintenanceCheck_inventory === void 0 ? void 0 : _maintenanceCheck_inventory.id) > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            href: \"/inventory/view?id=\".concat(maintenanceCheck.inventory.id),\n                            className: \"hover:underline\",\n                            children: maintenanceCheck.inventory.item\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                            lineNumber: 1080,\n                            columnNumber: 33\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: \"-\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                            lineNumber: 1086,\n                            columnNumber: 33\n                        }, this)\n                    }, void 0, false);\n                }\n            },\n            {\n                accessorKey: \"status\",\n                header: (param)=>{\n                    let { column } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_13__.DataTableSortHeader, {\n                        column: column,\n                        title: \"Status\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                        lineNumber: 1095,\n                        columnNumber: 21\n                    }, this);\n                },\n                cellAlignment: \"right\",\n                cell: (param)=>{\n                    let { row } = param;\n                    var _maintenanceCheck_isOverDue, _maintenanceCheck_isOverDue1;\n                    const maintenanceCheck = row.original;\n                    if (!maintenanceCheck) {\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: \"-\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                            lineNumber: 1102,\n                            columnNumber: 32\n                        }, this);\n                    }\n                    const overDueStatus = (_maintenanceCheck_isOverDue = maintenanceCheck.isOverDue) === null || _maintenanceCheck_isOverDue === void 0 ? void 0 : _maintenanceCheck_isOverDue.status;\n                    const overDueDays = (_maintenanceCheck_isOverDue1 = maintenanceCheck.isOverDue) === null || _maintenanceCheck_isOverDue1 === void 0 ? void 0 : _maintenanceCheck_isOverDue1.day;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: overDueStatus === \"High\" ? !bp[\"tablet-lg\"] ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \" items-end mr-2.5 text-nowrap w-fit\\n                                                    \".concat(overDueStatus === \"High\" ? \"alert text-sm !px-1.5 !py-0.5 xs:px-3 xs:py-1 whitespace-nowrap\" : \"\", \"\\n                                                    \"),\n                            children: overDueDays * -1 + \" days ago\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                            lineNumber: 1112,\n                            columnNumber: 37\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatusBadge, {\n                            maintenanceCheck: maintenanceCheck\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                            lineNumber: 1119,\n                            columnNumber: 37\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatusBadge, {\n                            maintenanceCheck: maintenanceCheck\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                            lineNumber: 1124,\n                            columnNumber: 33\n                        }, this)\n                    }, void 0, false);\n                },\n                sortingFn: (rowA, rowB)=>{\n                    var _rowA_original_isOverDue, _rowA_original, _rowB_original_isOverDue, _rowB_original;\n                    const valueA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : (_rowA_original_isOverDue = _rowA_original.isOverDue) === null || _rowA_original_isOverDue === void 0 ? void 0 : _rowA_original_isOverDue.days) || \"\";\n                    const valueB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : (_rowB_original_isOverDue = _rowB_original.isOverDue) === null || _rowB_original_isOverDue === void 0 ? void 0 : _rowB_original_isOverDue.days) || \"\";\n                    return valueA.localeCompare(valueB);\n                }\n            }\n        ]);\n    const columns = createMaintenanceColumns(crewInfo || [], getVesselWithIcon);\n    // Row status evaluator for maintenance tasks\n    const getMaintenanceRowStatus = (maintenanceCheck)=>{\n        var _maintenanceCheck_isOverDue;\n        // Skip completed, archived, or draft tasks\n        if (maintenanceCheck.status === \"Completed\" || maintenanceCheck.archived || maintenanceCheck.status === \"Save_As_Draft\") {\n            return \"normal\";\n        }\n        const overDueStatus = (_maintenanceCheck_isOverDue = maintenanceCheck.isOverDue) === null || _maintenanceCheck_isOverDue === void 0 ? void 0 : _maintenanceCheck_isOverDue.status;\n        // Use the pre-calculated status values from the system\n        switch(overDueStatus){\n            case \"High\":\n                return \"overdue\" // Red highlighting\n                ;\n            case \"Upcoming\":\n                return \"upcoming\" // Orange highlighting\n                ;\n            case \"Medium\":\n            case \"Open\":\n            default:\n                return \"normal\" // No highlighting\n                ;\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_list_header__WEBPACK_IMPORTED_MODULE_15__.ListHeader, {\n                title: \"Maintenance\",\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_lib_icons__WEBPACK_IMPORTED_MODULE_22__.SealogsMaintenanceIcon, {\n                    className: \"h-12 w-12 ring-1 p-1 rounded-full bg-[#fff]\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                    lineNumber: 1177,\n                    columnNumber: 21\n                }, void 0),\n                actions: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_filter_components_maintenance_actions__WEBPACK_IMPORTED_MODULE_14__.MaintenanceFilterActions, {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                    lineNumber: 1179,\n                    columnNumber: 26\n                }, void 0),\n                titleClassName: \"\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                lineNumber: 1174,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-16\",\n                children: maintenanceChecks && vessels ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_filteredTable__WEBPACK_IMPORTED_MODULE_12__.DataTable, {\n                    columns: columns,\n                    data: filteredMaintenanceChecks || [],\n                    pageSize: 20,\n                    onChange: handleFilterOnChange,\n                    rowStatus: getMaintenanceRowStatus\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                    lineNumber: 1184,\n                    columnNumber: 21\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_skeletons__WEBPACK_IMPORTED_MODULE_3__.TableSkeleton, {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                    lineNumber: 1192,\n                    columnNumber: 21\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                lineNumber: 1182,\n                columnNumber: 13\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s1(TaskList, \"LFHio0/i4l3OQsePNWySacFXTG4=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_7__.usePathname,\n        next_navigation__WEBPACK_IMPORTED_MODULE_7__.useSearchParams,\n        _app_lib_vessel_icon_helper__WEBPACK_IMPORTED_MODULE_19__.useVesselIconData,\n        _components_hooks_useBreakpoints__WEBPACK_IMPORTED_MODULE_20__.useBreakpoints,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_26__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_26__.useLazyQuery\n    ];\n});\n_c2 = TaskList;\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"StatusBadge\");\n$RefreshReg$(_c1, \"MaintenanceTable\");\n$RefreshReg$(_c2, \"TaskList\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/maintenance/list/list.tsx\n"));

/***/ })

});