"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/app/ui/maintenance/list/list.tsx":
/*!**********************************************!*\
  !*** ./src/app/ui/maintenance/list/list.tsx ***!
  \**********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MaintenanceTable: function() { return /* binding */ MaintenanceTable; },\n/* harmony export */   StatusBadge: function() { return /* binding */ StatusBadge; },\n/* harmony export */   createMaintenanceTableColumns: function() { return /* binding */ createMaintenanceTableColumns; },\n/* harmony export */   \"default\": function() { return /* binding */ TaskList; },\n/* harmony export */   getVesselDetails: function() { return /* binding */ getVesselDetails; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/react/hooks/useLazyQuery.js\");\n/* harmony import */ var _app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/app/lib/graphQL/query */ \"(app-pages-browser)/./src/app/lib/graphQL/query/index.ts\");\n/* harmony import */ var _components_skeletons__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/skeletons */ \"(app-pages-browser)/./src/components/skeletons.tsx\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/api/link.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! dayjs */ \"(app-pages-browser)/./node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/dayjs.min.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(dayjs__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var lodash_isEmpty__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! lodash/isEmpty */ \"(app-pages-browser)/./node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/isEmpty.js\");\n/* harmony import */ var lodash_isEmpty__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(lodash_isEmpty__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/app/helpers/userHelper */ \"(app-pages-browser)/./src/app/helpers/userHelper.ts\");\n/* harmony import */ var _reporting_maintenance_status_activity_report__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../../reporting/maintenance-status-activity-report */ \"(app-pages-browser)/./src/app/ui/reporting/maintenance-status-activity-report.tsx\");\n/* harmony import */ var _app_helpers_csvHelper__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/app/helpers/csvHelper */ \"(app-pages-browser)/./src/app/helpers/csvHelper.ts\");\n/* harmony import */ var _app_helpers_pdfHelper__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/app/helpers/pdfHelper */ \"(app-pages-browser)/./src/app/helpers/pdfHelper.ts\");\n/* harmony import */ var _components_filteredTable__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/filteredTable */ \"(app-pages-browser)/./src/components/filteredTable.tsx\");\n/* harmony import */ var _components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/data-table-sort-header */ \"(app-pages-browser)/./src/components/data-table-sort-header.tsx\");\n/* harmony import */ var _components_filter_components_maintenance_actions__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/components/filter/components/maintenance-actions */ \"(app-pages-browser)/./src/components/filter/components/maintenance-actions.tsx\");\n/* harmony import */ var _components_ui_list_header__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/components/ui/list-header */ \"(app-pages-browser)/./src/components/ui/list-header.tsx\");\n/* harmony import */ var _components_ui_avatar__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/components/ui/avatar */ \"(app-pages-browser)/./src/components/ui/avatar.tsx\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.ts\");\n/* harmony import */ var _vessels_vesel_icon__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! ../../vessels/vesel-icon */ \"(app-pages-browser)/./src/app/ui/vessels/vesel-icon.tsx\");\n/* harmony import */ var _app_lib_vessel_icon_helper__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @/app/lib/vessel-icon-helper */ \"(app-pages-browser)/./src/app/lib/vessel-icon-helper.tsx\");\n/* harmony import */ var _components_hooks_useBreakpoints__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! @/components/hooks/useBreakpoints */ \"(app-pages-browser)/./src/components/hooks/useBreakpoints.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _app_lib_icons__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! @/app/lib/icons */ \"(app-pages-browser)/./src/app/lib/icons/index.ts\");\n/* harmony import */ var _queries__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! ./queries */ \"(app-pages-browser)/./src/app/ui/maintenance/list/queries.ts\");\n/* harmony import */ var _app_offline_models_vessel__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! @/app/offline/models/vessel */ \"(app-pages-browser)/./src/app/offline/models/vessel.js\");\n/* harmony import */ var _app_lib_utils__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! @/app/lib/utils */ \"(app-pages-browser)/./src/app/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ getVesselDetails,createMaintenanceTableColumns,StatusBadge,MaintenanceTable,default auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// Helper functions\nconst getCrewInitials = (firstName, surname)=>{\n    var _firstName_charAt, _surname_charAt;\n    if (!firstName && !surname) return \"??\";\n    const first = (firstName === null || firstName === void 0 ? void 0 : (_firstName_charAt = firstName.charAt(0)) === null || _firstName_charAt === void 0 ? void 0 : _firstName_charAt.toUpperCase()) || \"\";\n    const last = (surname === null || surname === void 0 ? void 0 : (_surname_charAt = surname.charAt(0)) === null || _surname_charAt === void 0 ? void 0 : _surname_charAt.toUpperCase()) || \"\";\n    return \"\".concat(first).concat(last) || \"??\";\n};\nconst getVesselInitials = (title)=>{\n    if (!title) return \"??\";\n    const words = title.split(\" \").filter((word)=>word.length > 0);\n    if (words.length === 1) {\n        return words[0].substring(0, 2).toUpperCase();\n    }\n    return words.slice(0, 2).map((word)=>word.charAt(0).toUpperCase()).join(\"\");\n};\nconst getCrewDetails = (assignedToID, crewInfo)=>{\n    return crewInfo === null || crewInfo === void 0 ? void 0 : crewInfo.find((crew)=>crew.id === assignedToID.toString());\n};\nconst getVesselDetails = (vesselID, vessels)=>{\n    return vessels === null || vessels === void 0 ? void 0 : vessels.find((vessel)=>vessel.id === vesselID);\n};\n// Unified maintenance table column factory\nconst createMaintenanceTableColumns = (config)=>{\n    const { showVessel = true, showMobileCards = false, crewInfo, vessels, getVesselWithIcon, pathname, searchParams } = config;\n    return (0,_components_filteredTable__WEBPACK_IMPORTED_MODULE_12__.createColumns)([\n        // Title Column\n        {\n            accessorKey: \"title\",\n            header: (param)=>{\n                let { column } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_13__.DataTableSortHeader, {\n                    column: column,\n                    title: \"Title\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                    lineNumber: 95,\n                    columnNumber: 17\n                }, undefined);\n            },\n            cell: (param)=>{\n                let { row } = param;\n                var _maintenanceCheck_isOverDue, _maintenanceCheck_basicComponent, _maintenanceCheck_assignedTo;\n                const maintenanceCheck = row.original;\n                const overDueStatus = (_maintenanceCheck_isOverDue = maintenanceCheck.isOverDue) === null || _maintenanceCheck_isOverDue === void 0 ? void 0 : _maintenanceCheck_isOverDue.status;\n                if (showMobileCards) {\n                    var _maintenanceCheck_assignedTo1, _maintenanceCheck_inventory, _maintenanceCheck_assignedTo2, _maintenanceCheck_basicComponent1;\n                    const crewDetails = getCrewDetails(((_maintenanceCheck_assignedTo1 = maintenanceCheck.assignedTo) === null || _maintenanceCheck_assignedTo1 === void 0 ? void 0 : _maintenanceCheck_assignedTo1.id) || 0, crewInfo);\n                    var _maintenanceCheck_name, _maintenanceCheck_name1;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_21__.Card, {\n                                className: \"xs:hidden space-y-3 p-2.5 min-h-20 w-full shadow-none rounded-none bg-transparent\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_21__.CardContent, {\n                                    className: \"p-0 space-y-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                            href: \"/maintenance?taskID=\".concat(maintenanceCheck.id, \"&redirect_to=\").concat(pathname, \"?\").concat(searchParams.toString()),\n                                            className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_25__.cn)(\"text-base\", overDueStatus === \"High\" ? \"text-cinnabar-500 hover:text-cinnabar-700\" : overDueStatus === \"Upcoming\" ? \"text-fire-bush-500 hover:text-fire-bush-700\" : \"hover:text-curious-blue-400\"),\n                                            children: (_maintenanceCheck_name = maintenanceCheck.name) !== null && _maintenanceCheck_name !== void 0 ? _maintenanceCheck_name : \"Task #\".concat(maintenanceCheck.id, \" (No Name) - \").concat(dayjs__WEBPACK_IMPORTED_MODULE_5___default()(maintenanceCheck.created).format(\"DD/MM/YYYY\"))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                            lineNumber: 112,\n                                            columnNumber: 37\n                                        }, undefined),\n                                        ((_maintenanceCheck_inventory = maintenanceCheck.inventory) === null || _maintenanceCheck_inventory === void 0 ? void 0 : _maintenanceCheck_inventory.id) > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-col\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xs text-muted-foreground\",\n                                                    children: \"Inventory item\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                                    lineNumber: 131,\n                                                    columnNumber: 45\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                    href: \"/inventory/view?id=\".concat(maintenanceCheck.inventory.id),\n                                                    className: \"hover:underline text-sm\",\n                                                    children: maintenanceCheck.inventory.item\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                                    lineNumber: 134,\n                                                    columnNumber: 45\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                            lineNumber: 130,\n                                            columnNumber: 41\n                                        }, undefined),\n                                        ((_maintenanceCheck_assignedTo2 = maintenanceCheck.assignedTo) === null || _maintenanceCheck_assignedTo2 === void 0 ? void 0 : _maintenanceCheck_assignedTo2.id) > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_16__.Avatar, {\n                                            variant: \"secondary\",\n                                            className: \"h-8 w-8\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_16__.AvatarFallback, {\n                                                className: \"text-xs\",\n                                                children: getCrewInitials(crewDetails === null || crewDetails === void 0 ? void 0 : crewDetails.firstName, crewDetails === null || crewDetails === void 0 ? void 0 : crewDetails.surname)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                                lineNumber: 150,\n                                                columnNumber: 45\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                            lineNumber: 147,\n                                            columnNumber: 41\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                    lineNumber: 111,\n                                    columnNumber: 33\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                lineNumber: 110,\n                                columnNumber: 29\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"hidden xs:block\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center flex-nowrap gap-2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                            href: \"/maintenance?taskID=\".concat(maintenanceCheck.id, \"&redirect_to=\").concat(pathname, \"?\").concat(searchParams.toString()),\n                                            className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_25__.cn)(\"text-base\", overDueStatus === \"High\" ? \"text-cinnabar-500 hover:text-cinnabar-700\" : overDueStatus === \"Upcoming\" ? \"text-fire-bush-500 hover:text-fire-bush-700\" : \"hover:text-curious-blue-400\"),\n                                            children: (_maintenanceCheck_name1 = maintenanceCheck.name) !== null && _maintenanceCheck_name1 !== void 0 ? _maintenanceCheck_name1 : \"Task #\".concat(maintenanceCheck.id, \" (No Name) - \").concat(dayjs__WEBPACK_IMPORTED_MODULE_5___default()(maintenanceCheck.created).format(\"DD/MM/YYYY\"))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                            lineNumber: 164,\n                                            columnNumber: 37\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                        lineNumber: 163,\n                                        columnNumber: 33\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"md:hidden\",\n                                        children: ((_maintenanceCheck_basicComponent1 = maintenanceCheck.basicComponent) === null || _maintenanceCheck_basicComponent1 === void 0 ? void 0 : _maintenanceCheck_basicComponent1.id) > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                            href: \"/vessel/info?id=\".concat(maintenanceCheck.basicComponent.id),\n                                            className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_25__.cn)(\"text-base\", overDueStatus === \"High\" ? \"text-cinnabar-500 hover:text-cinnabar-700\" : overDueStatus === \"Upcoming\" ? \"text-fire-bush-500 hover:text-fire-bush-700\" : \"hover:text-curious-blue-400\"),\n                                            children: maintenanceCheck.basicComponent.title\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                            lineNumber: 184,\n                                            columnNumber: 41\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                        lineNumber: 181,\n                                        columnNumber: 33\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                lineNumber: 162,\n                                columnNumber: 29\n                            }, undefined)\n                        ]\n                    }, void 0, true);\n                }\n                var _maintenanceCheck_name2;\n                // Simple layout for non-mobile-card version\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                href: \"/maintenance?taskID=\".concat(maintenanceCheck.id, \"&redirect_to=\").concat(pathname, \"?\").concat(searchParams.toString()),\n                                className: \"\".concat(overDueStatus === \"High\" ? \"text-cinnabar-500 hover:text-cinnabar-700\" : overDueStatus === \"Upcoming\" ? \"text-fire-bush-500 hover:text-fire-bush-700\" : \"hover:text-curious-blue-400\"),\n                                children: (_maintenanceCheck_name2 = maintenanceCheck.name) !== null && _maintenanceCheck_name2 !== void 0 ? _maintenanceCheck_name2 : \"Task #\".concat(maintenanceCheck.id, \" (No Name) - \").concat(dayjs__WEBPACK_IMPORTED_MODULE_5___default()(maintenanceCheck.created).format(\"DD/MM/YYYY\"))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                lineNumber: 211,\n                                columnNumber: 29\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                            lineNumber: 210,\n                            columnNumber: 25\n                        }, undefined),\n                        showVessel && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:hidden\",\n                            children: ((_maintenanceCheck_basicComponent = maintenanceCheck.basicComponent) === null || _maintenanceCheck_basicComponent === void 0 ? void 0 : _maintenanceCheck_basicComponent.id) > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                href: \"/vessel/info?id=\".concat(maintenanceCheck.basicComponent.id),\n                                className: \"text-sm text-muted-foreground hover:underline\",\n                                children: maintenanceCheck.basicComponent.title\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                lineNumber: 231,\n                                columnNumber: 37\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                            lineNumber: 229,\n                            columnNumber: 29\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"md:hidden space-y-2\",\n                            children: ((_maintenanceCheck_assignedTo = maintenanceCheck.assignedTo) === null || _maintenanceCheck_assignedTo === void 0 ? void 0 : _maintenanceCheck_assignedTo.id) > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-muted-foreground\",\n                                        children: [\n                                            \"Assigned to:\",\n                                            \" \"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                        lineNumber: 244,\n                                        columnNumber: 37\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        href: \"/crew/info?id=\".concat(maintenanceCheck.assignedTo.id),\n                                        className: \"hover:underline\",\n                                        children: maintenanceCheck.assignedTo.name\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                        lineNumber: 247,\n                                        columnNumber: 37\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                lineNumber: 243,\n                                columnNumber: 33\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                            lineNumber: 241,\n                            columnNumber: 25\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                    lineNumber: 209,\n                    columnNumber: 21\n                }, undefined);\n            },\n            sortingFn: (rowA, rowB)=>{\n                var _rowA_original, _rowB_original;\n                const valueA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : _rowA_original.name) || \"\";\n                const valueB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : _rowB_original.name) || \"\";\n                return valueA.localeCompare(valueB);\n            }\n        },\n        // Location/Vessel Column (conditional)\n        ...showVessel ? [\n            {\n                accessorKey: \"location\",\n                header: (param)=>{\n                    let { column } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_13__.DataTableSortHeader, {\n                        column: column,\n                        title: \"Location\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                        lineNumber: 274,\n                        columnNumber: 27\n                    }, undefined);\n                },\n                cellAlignment: \"left\",\n                breakpoint: showMobileCards ? \"laptop\" : \"landscape\",\n                cell: (param)=>{\n                    let { row } = param;\n                    var _maintenanceCheck_basicComponent, _maintenanceCheck_basicComponent1;\n                    const maintenanceCheck = row.original;\n                    const vesselDetails = getVesselDetails(((_maintenanceCheck_basicComponent = maintenanceCheck.basicComponent) === null || _maintenanceCheck_basicComponent === void 0 ? void 0 : _maintenanceCheck_basicComponent.id) || 0, vessels);\n                    if (showMobileCards && getVesselWithIcon) {\n                        var _maintenanceCheck_basicComponent2, _maintenanceCheck_basicComponent3;\n                        const vesselWithIcon = getVesselWithIcon(((_maintenanceCheck_basicComponent2 = maintenanceCheck.basicComponent) === null || _maintenanceCheck_basicComponent2 === void 0 ? void 0 : _maintenanceCheck_basicComponent2.id) || 0, vesselDetails);\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: ((_maintenanceCheck_basicComponent3 = maintenanceCheck.basicComponent) === null || _maintenanceCheck_basicComponent3 === void 0 ? void 0 : _maintenanceCheck_basicComponent3.id) > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2.5\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_17__.Tooltip, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_17__.TooltipTrigger, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"min-w-fit\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_vessels_vesel_icon__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                        vessel: vesselWithIcon\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                                        lineNumber: 304,\n                                                        columnNumber: 59\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                                    lineNumber: 303,\n                                                    columnNumber: 55\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                                lineNumber: 302,\n                                                columnNumber: 51\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_17__.TooltipContent, {\n                                                children: maintenanceCheck.basicComponent.title\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                                lineNumber: 311,\n                                                columnNumber: 51\n                                            }, undefined)\n                                        ]\n                                    }, vesselDetails === null || vesselDetails === void 0 ? void 0 : vesselDetails.id, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                        lineNumber: 301,\n                                        columnNumber: 47\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        href: \"/vessel/info?id=\".concat(maintenanceCheck.basicComponent.id),\n                                        className: \"hover:underline\",\n                                        children: maintenanceCheck.basicComponent.title\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                        lineNumber: 319,\n                                        columnNumber: 47\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                lineNumber: 300,\n                                columnNumber: 43\n                            }, undefined)\n                        }, void 0, false);\n                    }\n                    // Simple layout\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"hidden lg:block\",\n                        children: ((_maintenanceCheck_basicComponent1 = maintenanceCheck.basicComponent) === null || _maintenanceCheck_basicComponent1 === void 0 ? void 0 : _maintenanceCheck_basicComponent1.id) > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2.5\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_16__.Avatar, {\n                                    size: \"sm\",\n                                    variant: \"secondary\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_16__.AvatarFallback, {\n                                        className: \"text-xs\",\n                                        children: getVesselInitials((vesselDetails === null || vesselDetails === void 0 ? void 0 : vesselDetails.title) || maintenanceCheck.basicComponent.title || undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                        lineNumber: 339,\n                                        columnNumber: 47\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                    lineNumber: 338,\n                                    columnNumber: 43\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    href: \"/vessel/info?id=\".concat(maintenanceCheck.basicComponent.id),\n                                    className: \"hover:underline\",\n                                    children: maintenanceCheck.basicComponent.title\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                    lineNumber: 349,\n                                    columnNumber: 43\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                            lineNumber: 337,\n                            columnNumber: 39\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                        lineNumber: 335,\n                        columnNumber: 31\n                    }, undefined);\n                },\n                sortingFn: (rowA, rowB)=>{\n                    var _rowA_original_basicComponent, _rowA_original, _rowB_original_basicComponent, _rowB_original;\n                    const valueA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : (_rowA_original_basicComponent = _rowA_original.basicComponent) === null || _rowA_original_basicComponent === void 0 ? void 0 : _rowA_original_basicComponent.title) || \"\";\n                    const valueB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : (_rowB_original_basicComponent = _rowB_original.basicComponent) === null || _rowB_original_basicComponent === void 0 ? void 0 : _rowB_original_basicComponent.title) || \"\";\n                    return valueA.localeCompare(valueB);\n                }\n            }\n        ] : [],\n        // Assigned To Column\n        {\n            accessorKey: \"assigned\",\n            header: (param)=>{\n                let { column } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_13__.DataTableSortHeader, {\n                    column: column,\n                    title: \"Assigned to\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                    lineNumber: 380,\n                    columnNumber: 17\n                }, undefined);\n            },\n            cellAlignment: \"left\",\n            breakpoint: showMobileCards ? \"tablet-md\" : \"landscape\",\n            cell: (param)=>{\n                let { row } = param;\n                var _maintenanceCheck_assignedTo, _maintenanceCheck_assignedTo1;\n                const maintenanceCheck = row.original;\n                const crewDetails = getCrewDetails(((_maintenanceCheck_assignedTo = maintenanceCheck.assignedTo) === null || _maintenanceCheck_assignedTo === void 0 ? void 0 : _maintenanceCheck_assignedTo.id) || 0, crewInfo);\n                const displayClass = showMobileCards ? \"hidden md:block\" : \"hidden md:block\";\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: displayClass,\n                    children: ((_maintenanceCheck_assignedTo1 = maintenanceCheck.assignedTo) === null || _maintenanceCheck_assignedTo1 === void 0 ? void 0 : _maintenanceCheck_assignedTo1.id) > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2.5\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_16__.Avatar, {\n                                variant: \"secondary\",\n                                className: \"h-8 w-8\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_16__.AvatarFallback, {\n                                    className: \"text-xs\",\n                                    children: getCrewInitials(crewDetails === null || crewDetails === void 0 ? void 0 : crewDetails.firstName, crewDetails === null || crewDetails === void 0 ? void 0 : crewDetails.surname)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                    lineNumber: 402,\n                                    columnNumber: 37\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                lineNumber: 401,\n                                columnNumber: 33\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                href: \"/crew/info?id=\".concat(maintenanceCheck.assignedTo.id),\n                                className: \"hover:underline \".concat(showMobileCards ? \"hidden tablet-md:block\" : \"hidden lg:block\"),\n                                children: maintenanceCheck.assignedTo.name\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                lineNumber: 409,\n                                columnNumber: 33\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                        lineNumber: 400,\n                        columnNumber: 29\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                    lineNumber: 398,\n                    columnNumber: 21\n                }, undefined);\n            },\n            sortingFn: (rowA, rowB)=>{\n                var _rowA_original_assignedTo, _rowA_original, _rowB_original_assignedTo, _rowB_original;\n                const valueA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : (_rowA_original_assignedTo = _rowA_original.assignedTo) === null || _rowA_original_assignedTo === void 0 ? void 0 : _rowA_original_assignedTo.name) || \"\";\n                const valueB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : (_rowB_original_assignedTo = _rowB_original.assignedTo) === null || _rowB_original_assignedTo === void 0 ? void 0 : _rowB_original_assignedTo.name) || \"\";\n                return valueA.localeCompare(valueB);\n            }\n        },\n        // Inventory Column\n        {\n            accessorKey: \"inventory\",\n            header: \"Inventory item\",\n            cellAlignment: \"left\",\n            breakpoint: showMobileCards ? \"phablet\" : \"tablet-lg\",\n            cell: (param)=>{\n                let { row } = param;\n                var _maintenanceCheck_inventory;\n                const maintenanceCheck = row.original;\n                const displayClass = showMobileCards ? \"\" : \"hidden md:block\";\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: displayClass,\n                    children: ((_maintenanceCheck_inventory = maintenanceCheck.inventory) === null || _maintenanceCheck_inventory === void 0 ? void 0 : _maintenanceCheck_inventory.id) > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        href: \"/inventory/view?id=\".concat(maintenanceCheck.inventory.id),\n                        className: \"hover:underline\",\n                        children: maintenanceCheck.inventory.item\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                        lineNumber: 448,\n                        columnNumber: 29\n                    }, undefined) : showMobileCards && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        children: \"-\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                        lineNumber: 454,\n                        columnNumber: 48\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                    lineNumber: 446,\n                    columnNumber: 21\n                }, undefined);\n            }\n        },\n        // Status Column\n        {\n            accessorKey: \"status\",\n            header: (param)=>{\n                let { column } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_13__.DataTableSortHeader, {\n                    column: column,\n                    title: \"Status\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                    lineNumber: 465,\n                    columnNumber: 17\n                }, undefined);\n            },\n            cellAlignment: \"right\",\n            cell: (param)=>{\n                let { row } = param;\n                const maintenanceCheck = row.original;\n                if (!maintenanceCheck) {\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: \"-\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                        lineNumber: 472,\n                        columnNumber: 28\n                    }, undefined);\n                }\n                if (showMobileCards) {\n                    var _maintenanceCheck_isOverDue, _maintenanceCheck_isOverDue1;\n                    const overDueStatus = (_maintenanceCheck_isOverDue = maintenanceCheck.isOverDue) === null || _maintenanceCheck_isOverDue === void 0 ? void 0 : _maintenanceCheck_isOverDue.status;\n                    const overDueDays = (_maintenanceCheck_isOverDue1 = maintenanceCheck.isOverDue) === null || _maintenanceCheck_isOverDue1 === void 0 ? void 0 : _maintenanceCheck_isOverDue1.day;\n                    const bp = (0,_components_hooks_useBreakpoints__WEBPACK_IMPORTED_MODULE_20__.useBreakpoints)();\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: overDueStatus === \"High\" ? !bp[\"tablet-lg\"] ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"items-end mr-2.5 text-nowrap w-fit \".concat(overDueStatus === \"High\" ? \"alert text-sm !px-1.5 !py-0.5 xs:px-3 xs:py-1 whitespace-nowrap\" : \"\"),\n                            children: overDueDays * -1 + \" days ago\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                            lineNumber: 484,\n                            columnNumber: 37\n                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatusBadge, {\n                            maintenanceCheck: maintenanceCheck\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                            lineNumber: 493,\n                            columnNumber: 37\n                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatusBadge, {\n                            maintenanceCheck: maintenanceCheck\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                            lineNumber: 498,\n                            columnNumber: 33\n                        }, undefined)\n                    }, void 0, false);\n                }\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"hidden md:block\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatusBadge, {\n                        maintenanceCheck: maintenanceCheck\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                        lineNumber: 508,\n                        columnNumber: 25\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                    lineNumber: 507,\n                    columnNumber: 21\n                }, undefined);\n            },\n            sortingFn: (rowA, rowB)=>{\n                var _rowA_original_isOverDue, _rowA_original, _rowB_original_isOverDue, _rowB_original;\n                const valueA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : (_rowA_original_isOverDue = _rowA_original.isOverDue) === null || _rowA_original_isOverDue === void 0 ? void 0 : _rowA_original_isOverDue.days) || \"\";\n                const valueB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : (_rowB_original_isOverDue = _rowB_original.isOverDue) === null || _rowB_original_isOverDue === void 0 ? void 0 : _rowB_original_isOverDue.days) || \"\";\n                return valueA.localeCompare(valueB);\n            }\n        }\n    ]);\n};\n// Status badge component following UI standards\nconst StatusBadge = (param)=>{\n    let { maintenanceCheck } = param;\n    var _maintenanceCheck_isOverDue, _maintenanceCheck_isOverDue1, _maintenanceCheck_isOverDue2, _maintenanceCheck_isOverDue3, _maintenanceCheck_isOverDue4, _maintenanceCheck_isOverDue5, _maintenanceCheck_isOverDue6, _maintenanceCheck_isOverDue7, _maintenanceCheck_isOverDue8, _maintenanceCheck_isOverDue9;\n    const isOverdue = (maintenanceCheck === null || maintenanceCheck === void 0 ? void 0 : (_maintenanceCheck_isOverDue = maintenanceCheck.isOverDue) === null || _maintenanceCheck_isOverDue === void 0 ? void 0 : _maintenanceCheck_isOverDue.status) === \"High\";\n    // Get status text\n    let statusText = \"\";\n    if ((maintenanceCheck === null || maintenanceCheck === void 0 ? void 0 : (_maintenanceCheck_isOverDue1 = maintenanceCheck.isOverDue) === null || _maintenanceCheck_isOverDue1 === void 0 ? void 0 : _maintenanceCheck_isOverDue1.status) && [\n        \"High\",\n        \"Medium\",\n        \"Low\"\n    ].includes(maintenanceCheck.isOverDue.status)) {\n        var _maintenanceCheck_isOverDue10;\n        statusText = maintenanceCheck === null || maintenanceCheck === void 0 ? void 0 : (_maintenanceCheck_isOverDue10 = maintenanceCheck.isOverDue) === null || _maintenanceCheck_isOverDue10 === void 0 ? void 0 : _maintenanceCheck_isOverDue10.days;\n    } else if ((maintenanceCheck === null || maintenanceCheck === void 0 ? void 0 : (_maintenanceCheck_isOverDue2 = maintenanceCheck.isOverDue) === null || _maintenanceCheck_isOverDue2 === void 0 ? void 0 : _maintenanceCheck_isOverDue2.status) === \"Completed\" && (maintenanceCheck === null || maintenanceCheck === void 0 ? void 0 : (_maintenanceCheck_isOverDue3 = maintenanceCheck.isOverDue) === null || _maintenanceCheck_isOverDue3 === void 0 ? void 0 : _maintenanceCheck_isOverDue3.days) === \"Save As Draft\") {\n        var _maintenanceCheck_isOverDue11;\n        statusText = maintenanceCheck === null || maintenanceCheck === void 0 ? void 0 : (_maintenanceCheck_isOverDue11 = maintenanceCheck.isOverDue) === null || _maintenanceCheck_isOverDue11 === void 0 ? void 0 : _maintenanceCheck_isOverDue11.days;\n    } else if ((maintenanceCheck === null || maintenanceCheck === void 0 ? void 0 : (_maintenanceCheck_isOverDue4 = maintenanceCheck.isOverDue) === null || _maintenanceCheck_isOverDue4 === void 0 ? void 0 : _maintenanceCheck_isOverDue4.status) === \"Upcoming\") {\n        var _maintenanceCheck_isOverDue12;\n        statusText = maintenanceCheck === null || maintenanceCheck === void 0 ? void 0 : (_maintenanceCheck_isOverDue12 = maintenanceCheck.isOverDue) === null || _maintenanceCheck_isOverDue12 === void 0 ? void 0 : _maintenanceCheck_isOverDue12.days;\n    } else if ((maintenanceCheck === null || maintenanceCheck === void 0 ? void 0 : (_maintenanceCheck_isOverDue5 = maintenanceCheck.isOverDue) === null || _maintenanceCheck_isOverDue5 === void 0 ? void 0 : _maintenanceCheck_isOverDue5.status) === \"Completed\" && lodash_isEmpty__WEBPACK_IMPORTED_MODULE_6___default()(maintenanceCheck === null || maintenanceCheck === void 0 ? void 0 : (_maintenanceCheck_isOverDue6 = maintenanceCheck.isOverDue) === null || _maintenanceCheck_isOverDue6 === void 0 ? void 0 : _maintenanceCheck_isOverDue6.days)) {\n        var _maintenanceCheck_isOverDue13;\n        statusText = maintenanceCheck === null || maintenanceCheck === void 0 ? void 0 : (_maintenanceCheck_isOverDue13 = maintenanceCheck.isOverDue) === null || _maintenanceCheck_isOverDue13 === void 0 ? void 0 : _maintenanceCheck_isOverDue13.status;\n    } else if ((maintenanceCheck === null || maintenanceCheck === void 0 ? void 0 : (_maintenanceCheck_isOverDue7 = maintenanceCheck.isOverDue) === null || _maintenanceCheck_isOverDue7 === void 0 ? void 0 : _maintenanceCheck_isOverDue7.status) === \"Completed\" && !lodash_isEmpty__WEBPACK_IMPORTED_MODULE_6___default()(maintenanceCheck === null || maintenanceCheck === void 0 ? void 0 : (_maintenanceCheck_isOverDue8 = maintenanceCheck.isOverDue) === null || _maintenanceCheck_isOverDue8 === void 0 ? void 0 : _maintenanceCheck_isOverDue8.days) && (maintenanceCheck === null || maintenanceCheck === void 0 ? void 0 : (_maintenanceCheck_isOverDue9 = maintenanceCheck.isOverDue) === null || _maintenanceCheck_isOverDue9 === void 0 ? void 0 : _maintenanceCheck_isOverDue9.days) !== \"Save As Draft\") {\n        var _maintenanceCheck_isOverDue14;\n        statusText = maintenanceCheck === null || maintenanceCheck === void 0 ? void 0 : (_maintenanceCheck_isOverDue14 = maintenanceCheck.isOverDue) === null || _maintenanceCheck_isOverDue14 === void 0 ? void 0 : _maintenanceCheck_isOverDue14.days;\n    }\n    // Only apply styling to overdue items, others are plain text\n    if (isOverdue) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n            className: \"alert w-fit inline-block rounded-md text-nowrap mr-2.5 text-sm xs:text-base !px-1.5 !py-0.5 xs:px-3 xs:py-1\",\n            children: statusText\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n            lineNumber: 628,\n            columnNumber: 13\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n        className: \"text-nowrap text-sm xs:text-base mr-2.5 !px-1.5 !py-0.5 xs:px-3 xs:py-1\",\n        children: statusText\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n        lineNumber: 635,\n        columnNumber: 9\n    }, undefined);\n};\n_c = StatusBadge;\n// Reusable MaintenanceTable component that accepts props\nfunction MaintenanceTable(param) {\n    let { maintenanceChecks, vessels, crewInfo, showVessel = false } = param;\n    _s();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_7__.usePathname)();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_7__.useSearchParams)();\n    const { getVesselWithIcon } = (0,_app_lib_vessel_icon_helper__WEBPACK_IMPORTED_MODULE_19__.useVesselIconData)();\n    const columns = createMaintenanceTableColumns({\n        showVessel: !showVessel,\n        showMobileCards: false,\n        crewInfo,\n        vessels,\n        getVesselWithIcon,\n        pathname,\n        searchParams\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_filteredTable__WEBPACK_IMPORTED_MODULE_12__.DataTable, {\n        columns: columns,\n        data: maintenanceChecks,\n        pageSize: 20,\n        showToolbar: false\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n        lineNumber: 668,\n        columnNumber: 9\n    }, this);\n}\n_s(MaintenanceTable, \"BtAwTu7MooJrudiBzqv5a/psZQo=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_7__.usePathname,\n        next_navigation__WEBPACK_IMPORTED_MODULE_7__.useSearchParams,\n        _app_lib_vessel_icon_helper__WEBPACK_IMPORTED_MODULE_19__.useVesselIconData\n    ];\n});\n_c1 = MaintenanceTable;\nfunction TaskList() {\n    _s1();\n    var _s = $RefreshSig$();\n    const [maintenanceChecks, setMaintenanceChecks] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    const [filteredMaintenanceChecks, setFilteredMaintenanceChecks] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    const [vessels, setVessels] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    const [crewInfo, setCrewInfo] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    const [filter, setFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [keywordFilter, setKeywordFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [permissions, setPermissions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_7__.usePathname)();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_7__.useSearchParams)();\n    const { getVesselWithIcon } = (0,_app_lib_vessel_icon_helper__WEBPACK_IMPORTED_MODULE_19__.useVesselIconData)();\n    const init_permissions = ()=>{\n        if (permissions) {\n            if ((0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_8__.hasPermission)(\"EDIT_TASK\", permissions)) {\n                setEdit_task(true);\n            } else {\n                setEdit_task(false);\n            }\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setPermissions(_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_8__.getPermissions);\n        init_permissions();\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (permissions) {\n            init_permissions();\n        }\n    }, [\n        permissions\n    ]);\n    const [queryMaintenanceChecks] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_26__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_2__.GET_MAINTENANCE_CHECK_LIST, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            const data = response.readComponentMaintenanceCheckList[0].list;\n            if (data) {\n                handleSetMaintenanceChecks(data);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"queryMaintenanceChecks error\", error);\n        }\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isLoading) {\n            loadMaintenanceChecks();\n            setIsLoading(false);\n        }\n    }, [\n        isLoading\n    ]);\n    const loadMaintenanceChecks = async ()=>{\n        await queryMaintenanceChecks({\n            variables: {\n                inventoryID: 0,\n                vesselID: 0\n            }\n        });\n    };\n    const handleSetVessels = (vessels)=>{\n        const activeVessels = vessels.filter((vessel)=>!vessel.archived);\n        const appendedData = activeVessels.map((item)=>({\n                ...item\n            }));\n        appendedData.push({\n            title: \"Other\",\n            id: 0\n        });\n        setVessels(appendedData);\n    };\n    const getVesselList = function(handleSetVessels) {\n        let offline = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : false;\n        _s();\n        const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n        const vesselModel = new _app_offline_models_vessel__WEBPACK_IMPORTED_MODULE_24__[\"default\"]();\n        (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n            if (isLoading) {\n                loadVessels();\n                setIsLoading(false);\n            }\n        }, [\n            isLoading\n        ]);\n        const [queryVessels] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_26__.useLazyQuery)(_queries__WEBPACK_IMPORTED_MODULE_23__.ReadVessels, {\n            fetchPolicy: \"cache-and-network\",\n            onCompleted: (queryVesselResponse)=>{\n                if (queryVesselResponse.readVessels.nodes) {\n                    handleSetVessels(queryVesselResponse.readVessels.nodes);\n                }\n            },\n            onError: (error)=>{\n                console.error(\"queryVessels error\", error);\n            }\n        });\n        const loadVessels = async ()=>{\n            if (offline) {\n                const response = await vesselModel.getAll();\n                handleSetVessels(response);\n            } else {\n                await queryVessels({\n                    variables: {\n                        limit: 200,\n                        offset: 0\n                    }\n                });\n            }\n        };\n    };\n    _s(getVesselList, \"pQOK42e7v9ItR64U9CP/qx2cQME=\", false, function() {\n        return [\n            _apollo_client__WEBPACK_IMPORTED_MODULE_26__.useLazyQuery\n        ];\n    });\n    getVesselList(handleSetVessels);\n    const handleSetMaintenanceChecks = (tasks)=>{\n        setMaintenanceChecks(tasks);\n        setFilteredMaintenanceChecks(tasks);\n        const appendedData = Array.from(new Set(tasks.filter((item)=>item.assignedTo.id > 0).map((item)=>item.assignedTo.id)));\n        loadCrewMemberInfo(appendedData);\n    };\n    const [queryCrewMemberInfo] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_26__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_2__.GET_CREW_BY_IDS, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            const data = response.readSeaLogsMembers.nodes;\n            if (data) {\n                setCrewInfo(data);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"queryCrewMemberInfo error\", error);\n        }\n    });\n    const loadCrewMemberInfo = async (crewId)=>{\n        await queryCrewMemberInfo({\n            variables: {\n                crewMemberIDs: crewId.length > 0 ? crewId : [\n                    0\n                ]\n            }\n        });\n    };\n    const handleFilterOnChange = (param)=>{\n        let { type, data } = param;\n        const searchFilter = {\n            ...filter\n        };\n        let filteredTasks = maintenanceChecks || [];\n        // Vessel filter\n        if (type === \"vessel\") {\n            if (Array.isArray(data) && data.length > 0) {\n                searchFilter.basicComponentID = {\n                    in: data.map((item)=>+item.value)\n                };\n            } else if (data && !Array.isArray(data)) {\n                searchFilter.basicComponentID = {\n                    eq: +data.value\n                };\n            } else {\n                delete searchFilter.basicComponentID;\n            }\n        }\n        // Status filter\n        if (type === \"status\") {\n            if (Array.isArray(data) && data.length > 0) {\n                searchFilter.status = {\n                    in: data.map((item)=>item.value)\n                };\n            } else if (data && !Array.isArray(data)) {\n                searchFilter.status = {\n                    eq: data.value\n                };\n            } else {\n                delete searchFilter.status;\n            }\n        }\n        // Assigned member filter\n        if (type === \"member\") {\n            if (Array.isArray(data) && data.length > 0) {\n                searchFilter.assignedToID = {\n                    in: data.map((item)=>+item.value)\n                };\n            } else if (data && !Array.isArray(data)) {\n                searchFilter.assignedToID = {\n                    eq: +data.value\n                };\n            } else {\n                delete searchFilter.assignedToID;\n            }\n        }\n        // Date range\n        if (type === \"dateRange\") {\n            if ((data === null || data === void 0 ? void 0 : data.startDate) && (data === null || data === void 0 ? void 0 : data.endDate)) {\n                searchFilter.expires = {\n                    gte: data.startDate,\n                    lte: data.endDate\n                };\n            } else {\n                delete searchFilter.expires;\n            }\n        }\n        // Category\n        if (type === \"category\") {\n            if (Array.isArray(data) && data.length > 0) {\n                searchFilter.maintenanceCategoryID = {\n                    in: data.map((item)=>+item.value)\n                };\n            } else if (data && !Array.isArray(data)) {\n                searchFilter.maintenanceCategoryID = {\n                    eq: +data.value\n                };\n            } else {\n                delete searchFilter.maintenanceCategoryID;\n            }\n        }\n        // Recurring filter - handle client-side filtering\n        let recurringFilter = null;\n        if (type === \"recurring\") {\n            if (data && !Array.isArray(data)) {\n                recurringFilter = data.value;\n            }\n        }\n        // Keyword filter\n        let keyFilter = keywordFilter;\n        if (type === \"keyword\" || keyFilter && keyFilter.length > 0) {\n            var _data_value;\n            const keyword = data === null || data === void 0 ? void 0 : (_data_value = data.value) === null || _data_value === void 0 ? void 0 : _data_value.trim().toLowerCase();\n            if (keyword && keyword.length > 0) {\n                filteredTasks = filteredTasks.filter((maintenanceCheck)=>[\n                        maintenanceCheck.name,\n                        maintenanceCheck.comments,\n                        maintenanceCheck.workOrderNumber\n                    ].some((field)=>field === null || field === void 0 ? void 0 : field.toLowerCase().includes(keyword)));\n                keyFilter = data.value;\n            } else {\n                keyFilter = null;\n            }\n        }\n        // Filtering based on current searchFilter\n        // Filter by vessel (basicComponentID)\n        if (searchFilter.basicComponentID) {\n            const ids = searchFilter.basicComponentID.in || [\n                searchFilter.basicComponentID.eq\n            ];\n            filteredTasks = filteredTasks.filter((mc)=>{\n                var _mc_basicComponent;\n                return ids.includes((_mc_basicComponent = mc.basicComponent) === null || _mc_basicComponent === void 0 ? void 0 : _mc_basicComponent.id);\n            });\n        }\n        // Filter by status\n        if (searchFilter.status) {\n            const statuses = searchFilter.status.in || [\n                searchFilter.status.eq\n            ];\n            filteredTasks = filteredTasks.filter((mc)=>statuses.includes(mc.status));\n        }\n        // Filter by assignedToID\n        if (searchFilter.assignedToID) {\n            const ids = searchFilter.assignedToID.in || [\n                searchFilter.assignedToID.eq\n            ];\n            filteredTasks = filteredTasks.filter((mc)=>{\n                var _mc_assignedTo;\n                return ids.includes((_mc_assignedTo = mc.assignedTo) === null || _mc_assignedTo === void 0 ? void 0 : _mc_assignedTo.id);\n            });\n        }\n        // Filter by category\n        if (searchFilter.maintenanceCategoryID) {\n            const ids = searchFilter.maintenanceCategoryID.in || [\n                searchFilter.maintenanceCategoryID.eq\n            ];\n            filteredTasks = filteredTasks.filter((mc)=>ids.includes(mc.maintenanceCategoryID));\n        }\n        // Filter by date range\n        if (searchFilter.expires && searchFilter.expires.gte && searchFilter.expires.lte) {\n            filteredTasks = filteredTasks.filter((mc)=>dayjs__WEBPACK_IMPORTED_MODULE_5___default()(mc.startDate).isAfter(dayjs__WEBPACK_IMPORTED_MODULE_5___default()(searchFilter.expires.gte)) && dayjs__WEBPACK_IMPORTED_MODULE_5___default()(mc.startDate).isBefore(dayjs__WEBPACK_IMPORTED_MODULE_5___default()(searchFilter.expires.lte)));\n        }\n        // Filter by recurring status\n        if (recurringFilter) {\n            if (recurringFilter === \"recurring\") {\n                // Recurring tasks have recurringID > 0\n                filteredTasks = filteredTasks.filter((mc)=>mc.recurringID > 0);\n            } else if (recurringFilter === \"one-off\") {\n                // One-off tasks have recurringID = 0 or null\n                filteredTasks = filteredTasks.filter((mc)=>!mc.recurringID || mc.recurringID === 0);\n            }\n        }\n        // Set updated filters\n        setFilter(searchFilter);\n        setKeywordFilter(keyFilter);\n        setFilteredMaintenanceChecks(filteredTasks);\n    // Optionally call API: loadMaintenanceChecks(searchFilter, keyFilter);\n    };\n    const downloadCsv = ()=>{\n        if (!maintenanceChecks || !vessels) {\n            return;\n        }\n        const csvEntries = [\n            [\n                \"task\",\n                \"location\",\n                \"assigned to\",\n                \"due\"\n            ]\n        ];\n        maintenanceChecks.filter((maintenanceCheck)=>maintenanceCheck.status !== \"Save_As_Draft\").forEach((maintenanceCheck)=>{\n            var _maintenanceCheck_assignedTo, _maintenanceCheck_assignedTo1;\n            const crewDetails = getCrewDetails(((_maintenanceCheck_assignedTo = maintenanceCheck.assignedTo) === null || _maintenanceCheck_assignedTo === void 0 ? void 0 : _maintenanceCheck_assignedTo.id) || 0, crewInfo);\n            const assignedToName = crewDetails ? \"\".concat(crewDetails.firstName, \" \").concat(crewDetails.surname) : ((_maintenanceCheck_assignedTo1 = maintenanceCheck.assignedTo) === null || _maintenanceCheck_assignedTo1 === void 0 ? void 0 : _maintenanceCheck_assignedTo1.name) || \"\";\n            csvEntries.push([\n                maintenanceCheck.name,\n                (vessels === null || vessels === void 0 ? void 0 : vessels.filter((vessel)=>{\n                    var _maintenanceCheck_basicComponent;\n                    return (vessel === null || vessel === void 0 ? void 0 : vessel.id) == ((_maintenanceCheck_basicComponent = maintenanceCheck.basicComponent) === null || _maintenanceCheck_basicComponent === void 0 ? void 0 : _maintenanceCheck_basicComponent.id);\n                }).map((vessel)=>vessel.title).join(\", \")) || \"\",\n                assignedToName,\n                (0,_reporting_maintenance_status_activity_report__WEBPACK_IMPORTED_MODULE_9__.dueStatusLabel)(maintenanceCheck.isOverDue)\n            ]);\n        });\n        (0,_app_helpers_csvHelper__WEBPACK_IMPORTED_MODULE_10__.exportCsv)(csvEntries);\n    };\n    const downloadPdf = ()=>{\n        if (!maintenanceChecks || !vessels) {\n            return;\n        }\n        const headers = [\n            [\n                \"Task Name\",\n                \"Location\",\n                \"Assigned To\",\n                \"Due\"\n            ]\n        ];\n        const body = maintenanceChecks.filter((maintenanceCheck)=>maintenanceCheck.status !== \"Save_As_Draft\").map((maintenanceCheck)=>{\n            var _maintenanceCheck_assignedTo, _maintenanceCheck_assignedTo1;\n            const crewDetails = getCrewDetails(((_maintenanceCheck_assignedTo = maintenanceCheck.assignedTo) === null || _maintenanceCheck_assignedTo === void 0 ? void 0 : _maintenanceCheck_assignedTo.id) || 0, crewInfo);\n            const assignedToName = crewDetails ? \"\".concat(crewDetails.firstName, \" \").concat(crewDetails.surname) : ((_maintenanceCheck_assignedTo1 = maintenanceCheck.assignedTo) === null || _maintenanceCheck_assignedTo1 === void 0 ? void 0 : _maintenanceCheck_assignedTo1.name) || \"\";\n            return [\n                maintenanceCheck.name,\n                (vessels === null || vessels === void 0 ? void 0 : vessels.filter((vessel)=>{\n                    var _maintenanceCheck_basicComponent;\n                    return (vessel === null || vessel === void 0 ? void 0 : vessel.id) == ((_maintenanceCheck_basicComponent = maintenanceCheck.basicComponent) === null || _maintenanceCheck_basicComponent === void 0 ? void 0 : _maintenanceCheck_basicComponent.id);\n                }).map((vessel)=>vessel.title).join(\", \")) || \"\",\n                assignedToName,\n                (0,_reporting_maintenance_status_activity_report__WEBPACK_IMPORTED_MODULE_9__.dueStatusLabel)(maintenanceCheck.isOverDue)\n            ];\n        });\n        (0,_app_helpers_pdfHelper__WEBPACK_IMPORTED_MODULE_11__.exportPdfTable)({\n            headers,\n            body\n        });\n    };\n    // Use the unified column factory for the main table\n    const columns = createMaintenanceTableColumns({\n        showVessel: true,\n        showMobileCards: true,\n        crewInfo: crewInfo || [],\n        vessels: vessels || [],\n        getVesselWithIcon,\n        pathname,\n        searchParams\n    });\n    // Row status evaluator for maintenance tasks\n    const getMaintenanceRowStatus = (maintenanceCheck)=>{\n        var _maintenanceCheck_isOverDue;\n        // Skip completed, archived, or draft tasks\n        if (maintenanceCheck.status === \"Completed\" || maintenanceCheck.archived || maintenanceCheck.status === \"Save_As_Draft\") {\n            return \"normal\";\n        }\n        const overDueStatus = (_maintenanceCheck_isOverDue = maintenanceCheck.isOverDue) === null || _maintenanceCheck_isOverDue === void 0 ? void 0 : _maintenanceCheck_isOverDue.status;\n        // Use the pre-calculated status values from the system\n        switch(overDueStatus){\n            case \"High\":\n                return \"overdue\" // Red highlighting\n                ;\n            case \"Upcoming\":\n                return \"upcoming\" // Orange highlighting\n                ;\n            case \"Medium\":\n            case \"Open\":\n            default:\n                return \"normal\" // No highlighting\n                ;\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_list_header__WEBPACK_IMPORTED_MODULE_15__.ListHeader, {\n                title: \"Maintenance\",\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_lib_icons__WEBPACK_IMPORTED_MODULE_22__.SealogsMaintenanceIcon, {\n                    className: \"h-12 w-12 ring-1 p-1 rounded-full bg-[#fff]\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                    lineNumber: 1119,\n                    columnNumber: 21\n                }, void 0),\n                actions: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_filter_components_maintenance_actions__WEBPACK_IMPORTED_MODULE_14__.MaintenanceFilterActions, {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                    lineNumber: 1121,\n                    columnNumber: 26\n                }, void 0),\n                titleClassName: \"\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                lineNumber: 1116,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-16\",\n                children: maintenanceChecks && vessels ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_filteredTable__WEBPACK_IMPORTED_MODULE_12__.DataTable, {\n                    columns: columns,\n                    data: filteredMaintenanceChecks || [],\n                    pageSize: 20,\n                    onChange: handleFilterOnChange,\n                    rowStatus: getMaintenanceRowStatus\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                    lineNumber: 1126,\n                    columnNumber: 21\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_skeletons__WEBPACK_IMPORTED_MODULE_3__.TableSkeleton, {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                    lineNumber: 1134,\n                    columnNumber: 21\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                lineNumber: 1124,\n                columnNumber: 13\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s1(TaskList, \"xSOgHd/j55S3Fly9VZAkRrmD3pw=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_7__.usePathname,\n        next_navigation__WEBPACK_IMPORTED_MODULE_7__.useSearchParams,\n        _app_lib_vessel_icon_helper__WEBPACK_IMPORTED_MODULE_19__.useVesselIconData,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_26__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_26__.useLazyQuery\n    ];\n});\n_c2 = TaskList;\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"StatusBadge\");\n$RefreshReg$(_c1, \"MaintenanceTable\");\n$RefreshReg$(_c2, \"TaskList\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/maintenance/list/list.tsx\n"));

/***/ })

});