"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/app/ui/maintenance/list/list.tsx":
/*!**********************************************!*\
  !*** ./src/app/ui/maintenance/list/list.tsx ***!
  \**********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MaintenanceTable: function() { return /* binding */ MaintenanceTable; },\n/* harmony export */   StatusBadge: function() { return /* binding */ StatusBadge; },\n/* harmony export */   \"default\": function() { return /* binding */ TaskList; },\n/* harmony export */   getVesselDetails: function() { return /* binding */ getVesselDetails; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/react/hooks/useLazyQuery.js\");\n/* harmony import */ var _app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/app/lib/graphQL/query */ \"(app-pages-browser)/./src/app/lib/graphQL/query/index.ts\");\n/* harmony import */ var _components_skeletons__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/skeletons */ \"(app-pages-browser)/./src/components/skeletons.tsx\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/api/link.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! dayjs */ \"(app-pages-browser)/./node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/dayjs.min.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(dayjs__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var lodash_isEmpty__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! lodash/isEmpty */ \"(app-pages-browser)/./node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/isEmpty.js\");\n/* harmony import */ var lodash_isEmpty__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(lodash_isEmpty__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/app/helpers/userHelper */ \"(app-pages-browser)/./src/app/helpers/userHelper.ts\");\n/* harmony import */ var _reporting_maintenance_status_activity_report__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../../reporting/maintenance-status-activity-report */ \"(app-pages-browser)/./src/app/ui/reporting/maintenance-status-activity-report.tsx\");\n/* harmony import */ var _app_helpers_csvHelper__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/app/helpers/csvHelper */ \"(app-pages-browser)/./src/app/helpers/csvHelper.ts\");\n/* harmony import */ var _app_helpers_pdfHelper__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/app/helpers/pdfHelper */ \"(app-pages-browser)/./src/app/helpers/pdfHelper.ts\");\n/* harmony import */ var _components_filteredTable__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/filteredTable */ \"(app-pages-browser)/./src/components/filteredTable.tsx\");\n/* harmony import */ var _components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/data-table-sort-header */ \"(app-pages-browser)/./src/components/data-table-sort-header.tsx\");\n/* harmony import */ var _components_filter_components_maintenance_actions__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/components/filter/components/maintenance-actions */ \"(app-pages-browser)/./src/components/filter/components/maintenance-actions.tsx\");\n/* harmony import */ var _components_ui_list_header__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/components/ui/list-header */ \"(app-pages-browser)/./src/components/ui/list-header.tsx\");\n/* harmony import */ var _components_ui_avatar__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/components/ui/avatar */ \"(app-pages-browser)/./src/components/ui/avatar.tsx\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.ts\");\n/* harmony import */ var _vessels_vesel_icon__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! ../../vessels/vesel-icon */ \"(app-pages-browser)/./src/app/ui/vessels/vesel-icon.tsx\");\n/* harmony import */ var _app_lib_vessel_icon_helper__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @/app/lib/vessel-icon-helper */ \"(app-pages-browser)/./src/app/lib/vessel-icon-helper.tsx\");\n/* harmony import */ var _components_hooks_useBreakpoints__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! @/components/hooks/useBreakpoints */ \"(app-pages-browser)/./src/components/hooks/useBreakpoints.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _app_lib_icons__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! @/app/lib/icons */ \"(app-pages-browser)/./src/app/lib/icons/index.ts\");\n/* harmony import */ var _queries__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! ./queries */ \"(app-pages-browser)/./src/app/ui/maintenance/list/queries.ts\");\n/* harmony import */ var _app_offline_models_vessel__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! @/app/offline/models/vessel */ \"(app-pages-browser)/./src/app/offline/models/vessel.js\");\n/* harmony import */ var _app_lib_utils__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! @/app/lib/utils */ \"(app-pages-browser)/./src/app/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ getVesselDetails,StatusBadge,MaintenanceTable,default auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// Helper functions\nconst getCrewInitials = (firstName, surname)=>{\n    var _firstName_charAt, _surname_charAt;\n    if (!firstName && !surname) return \"??\";\n    const first = (firstName === null || firstName === void 0 ? void 0 : (_firstName_charAt = firstName.charAt(0)) === null || _firstName_charAt === void 0 ? void 0 : _firstName_charAt.toUpperCase()) || \"\";\n    const last = (surname === null || surname === void 0 ? void 0 : (_surname_charAt = surname.charAt(0)) === null || _surname_charAt === void 0 ? void 0 : _surname_charAt.toUpperCase()) || \"\";\n    return \"\".concat(first).concat(last) || \"??\";\n};\nconst getVesselInitials = (title)=>{\n    if (!title) return \"??\";\n    const words = title.split(\" \").filter((word)=>word.length > 0);\n    if (words.length === 1) {\n        return words[0].substring(0, 2).toUpperCase();\n    }\n    return words.slice(0, 2).map((word)=>word.charAt(0).toUpperCase()).join(\"\");\n};\nconst getCrewDetails = (assignedToID, crewInfo)=>{\n    return crewInfo === null || crewInfo === void 0 ? void 0 : crewInfo.find((crew)=>crew.id === assignedToID.toString());\n};\nconst getVesselDetails = (vesselID, vessels)=>{\n    return vessels === null || vessels === void 0 ? void 0 : vessels.find((vessel)=>vessel.id === vesselID);\n};\n// Status badge component following UI standards\nconst StatusBadge = (param)=>{\n    let { maintenanceCheck } = param;\n    var _maintenanceCheck_isOverDue, _maintenanceCheck_isOverDue1, _maintenanceCheck_isOverDue2, _maintenanceCheck_isOverDue3, _maintenanceCheck_isOverDue4, _maintenanceCheck_isOverDue5, _maintenanceCheck_isOverDue6, _maintenanceCheck_isOverDue7, _maintenanceCheck_isOverDue8, _maintenanceCheck_isOverDue9;\n    const isOverdue = (maintenanceCheck === null || maintenanceCheck === void 0 ? void 0 : (_maintenanceCheck_isOverDue = maintenanceCheck.isOverDue) === null || _maintenanceCheck_isOverDue === void 0 ? void 0 : _maintenanceCheck_isOverDue.status) === \"High\";\n    // Get status text\n    let statusText = \"\";\n    if ((maintenanceCheck === null || maintenanceCheck === void 0 ? void 0 : (_maintenanceCheck_isOverDue1 = maintenanceCheck.isOverDue) === null || _maintenanceCheck_isOverDue1 === void 0 ? void 0 : _maintenanceCheck_isOverDue1.status) && [\n        \"High\",\n        \"Medium\",\n        \"Low\"\n    ].includes(maintenanceCheck.isOverDue.status)) {\n        var _maintenanceCheck_isOverDue10;\n        statusText = maintenanceCheck === null || maintenanceCheck === void 0 ? void 0 : (_maintenanceCheck_isOverDue10 = maintenanceCheck.isOverDue) === null || _maintenanceCheck_isOverDue10 === void 0 ? void 0 : _maintenanceCheck_isOverDue10.days;\n    } else if ((maintenanceCheck === null || maintenanceCheck === void 0 ? void 0 : (_maintenanceCheck_isOverDue2 = maintenanceCheck.isOverDue) === null || _maintenanceCheck_isOverDue2 === void 0 ? void 0 : _maintenanceCheck_isOverDue2.status) === \"Completed\" && (maintenanceCheck === null || maintenanceCheck === void 0 ? void 0 : (_maintenanceCheck_isOverDue3 = maintenanceCheck.isOverDue) === null || _maintenanceCheck_isOverDue3 === void 0 ? void 0 : _maintenanceCheck_isOverDue3.days) === \"Save As Draft\") {\n        var _maintenanceCheck_isOverDue11;\n        statusText = maintenanceCheck === null || maintenanceCheck === void 0 ? void 0 : (_maintenanceCheck_isOverDue11 = maintenanceCheck.isOverDue) === null || _maintenanceCheck_isOverDue11 === void 0 ? void 0 : _maintenanceCheck_isOverDue11.days;\n    } else if ((maintenanceCheck === null || maintenanceCheck === void 0 ? void 0 : (_maintenanceCheck_isOverDue4 = maintenanceCheck.isOverDue) === null || _maintenanceCheck_isOverDue4 === void 0 ? void 0 : _maintenanceCheck_isOverDue4.status) === \"Upcoming\") {\n        var _maintenanceCheck_isOverDue12;\n        statusText = maintenanceCheck === null || maintenanceCheck === void 0 ? void 0 : (_maintenanceCheck_isOverDue12 = maintenanceCheck.isOverDue) === null || _maintenanceCheck_isOverDue12 === void 0 ? void 0 : _maintenanceCheck_isOverDue12.days;\n    } else if ((maintenanceCheck === null || maintenanceCheck === void 0 ? void 0 : (_maintenanceCheck_isOverDue5 = maintenanceCheck.isOverDue) === null || _maintenanceCheck_isOverDue5 === void 0 ? void 0 : _maintenanceCheck_isOverDue5.status) === \"Completed\" && lodash_isEmpty__WEBPACK_IMPORTED_MODULE_6___default()(maintenanceCheck === null || maintenanceCheck === void 0 ? void 0 : (_maintenanceCheck_isOverDue6 = maintenanceCheck.isOverDue) === null || _maintenanceCheck_isOverDue6 === void 0 ? void 0 : _maintenanceCheck_isOverDue6.days)) {\n        var _maintenanceCheck_isOverDue13;\n        statusText = maintenanceCheck === null || maintenanceCheck === void 0 ? void 0 : (_maintenanceCheck_isOverDue13 = maintenanceCheck.isOverDue) === null || _maintenanceCheck_isOverDue13 === void 0 ? void 0 : _maintenanceCheck_isOverDue13.status;\n    } else if ((maintenanceCheck === null || maintenanceCheck === void 0 ? void 0 : (_maintenanceCheck_isOverDue7 = maintenanceCheck.isOverDue) === null || _maintenanceCheck_isOverDue7 === void 0 ? void 0 : _maintenanceCheck_isOverDue7.status) === \"Completed\" && !lodash_isEmpty__WEBPACK_IMPORTED_MODULE_6___default()(maintenanceCheck === null || maintenanceCheck === void 0 ? void 0 : (_maintenanceCheck_isOverDue8 = maintenanceCheck.isOverDue) === null || _maintenanceCheck_isOverDue8 === void 0 ? void 0 : _maintenanceCheck_isOverDue8.days) && (maintenanceCheck === null || maintenanceCheck === void 0 ? void 0 : (_maintenanceCheck_isOverDue9 = maintenanceCheck.isOverDue) === null || _maintenanceCheck_isOverDue9 === void 0 ? void 0 : _maintenanceCheck_isOverDue9.days) !== \"Save As Draft\") {\n        var _maintenanceCheck_isOverDue14;\n        statusText = maintenanceCheck === null || maintenanceCheck === void 0 ? void 0 : (_maintenanceCheck_isOverDue14 = maintenanceCheck.isOverDue) === null || _maintenanceCheck_isOverDue14 === void 0 ? void 0 : _maintenanceCheck_isOverDue14.days;\n    }\n    // Only apply styling to overdue items, others are plain text\n    if (isOverdue) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n            className: \"alert w-fit inline-block rounded-md text-nowrap mr-2.5 px-3 py-1 text-sm xs:text-base !px-1.5 !py-0.5 xs:px-3 xs:py-1\",\n            children: statusText\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n            lineNumber: 169,\n            columnNumber: 13\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n        className: \"text-nowrap text-sm xs:text-base !px-1.5 !py-0.5 xs:px-3 xs:py-1\",\n        children: statusText\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n        lineNumber: 175,\n        columnNumber: 12\n    }, undefined);\n};\n_c = StatusBadge;\n// Reusable MaintenanceTable component that accepts props\nfunction MaintenanceTable(param) {\n    let { maintenanceChecks, vessels, crewInfo, showVessel = false } = param;\n    _s();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_7__.usePathname)();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_7__.useSearchParams)();\n    const { vesselIconData, getVesselWithIcon } = (0,_app_lib_vessel_icon_helper__WEBPACK_IMPORTED_MODULE_19__.useVesselIconData)();\n    const columns = (0,_components_filteredTable__WEBPACK_IMPORTED_MODULE_12__.createColumns)([\n        {\n            accessorKey: \"title\",\n            header: (param)=>{\n                let { column } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_13__.DataTableSortHeader, {\n                    column: column,\n                    title: \"Title\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                    lineNumber: 197,\n                    columnNumber: 17\n                }, this);\n            },\n            cell: (param)=>{\n                let { row } = param;\n                var _maintenanceCheck_isOverDue, _maintenanceCheck_basicComponent, _maintenanceCheck_assignedTo;\n                const maintenanceCheck = row.original;\n                const overDueStatus = (_maintenanceCheck_isOverDue = maintenanceCheck.isOverDue) === null || _maintenanceCheck_isOverDue === void 0 ? void 0 : _maintenanceCheck_isOverDue.status;\n                var _maintenanceCheck_name;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                href: \"/maintenance?taskID=\".concat(maintenanceCheck.id, \"&redirect_to=\").concat(pathname, \"?\").concat(searchParams.toString()),\n                                className: \"\".concat(overDueStatus === \"High\" ? \"text-cinnabar-500 hover:text-cinnabar-700\" : overDueStatus === \"Upcoming\" ? \"text-fire-bush-500 hover:text-fire-bush-700\" : \"hover:text-curious-blue-400\"),\n                                children: (_maintenanceCheck_name = maintenanceCheck.name) !== null && _maintenanceCheck_name !== void 0 ? _maintenanceCheck_name : \"Task #\".concat(maintenanceCheck.id, \" (No Name) - \").concat(dayjs__WEBPACK_IMPORTED_MODULE_5___default()(maintenanceCheck.created).format(\"DD/MM/YYYY\"))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                lineNumber: 205,\n                                columnNumber: 29\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                            lineNumber: 204,\n                            columnNumber: 25\n                        }, this),\n                        !showVessel && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:hidden\",\n                            children: ((_maintenanceCheck_basicComponent = maintenanceCheck.basicComponent) === null || _maintenanceCheck_basicComponent === void 0 ? void 0 : _maintenanceCheck_basicComponent.id) > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                href: \"/vessel/info?id=\".concat(maintenanceCheck.basicComponent.id),\n                                className: \"text-sm text-muted-foreground hover:underline\",\n                                children: maintenanceCheck.basicComponent.title\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                lineNumber: 225,\n                                columnNumber: 37\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                            lineNumber: 223,\n                            columnNumber: 29\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"md:hidden space-y-2\",\n                            children: [\n                                ((_maintenanceCheck_assignedTo = maintenanceCheck.assignedTo) === null || _maintenanceCheck_assignedTo === void 0 ? void 0 : _maintenanceCheck_assignedTo.id) > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-muted-foreground\",\n                                            children: [\n                                                \"Assigned to:\",\n                                                \" \"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                            lineNumber: 238,\n                                            columnNumber: 37\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                            href: \"/crew/info?id=\".concat(maintenanceCheck.assignedTo.id),\n                                            className: \"hover:underline\",\n                                            children: maintenanceCheck.assignedTo.name\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                            lineNumber: 241,\n                                            columnNumber: 37\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                    lineNumber: 237,\n                                    columnNumber: 33\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatusBadge, {\n                                        maintenanceCheck: maintenanceCheck\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                        lineNumber: 249,\n                                        columnNumber: 33\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                    lineNumber: 248,\n                                    columnNumber: 29\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                            lineNumber: 235,\n                            columnNumber: 25\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                    lineNumber: 203,\n                    columnNumber: 21\n                }, this);\n            },\n            sortingFn: (rowA, rowB)=>{\n                var _rowA_original, _rowB_original;\n                const valueA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : _rowA_original.name) || \"\";\n                const valueB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : _rowB_original.name) || \"\";\n                return valueA.localeCompare(valueB);\n            }\n        },\n        ...showVessel ? [] : [\n            {\n                accessorKey: \"location\",\n                header: (param)=>{\n                    let { column } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_13__.DataTableSortHeader, {\n                        column: column,\n                        title: \"Location\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                        lineNumber: 272,\n                        columnNumber: 27\n                    }, this);\n                },\n                cellAlignment: \"left\",\n                cell: (param)=>{\n                    let { row } = param;\n                    var _maintenanceCheck_basicComponent, _maintenanceCheck_basicComponent1;\n                    const maintenanceCheck = row.original;\n                    const vesselDetails = getVesselDetails(((_maintenanceCheck_basicComponent = maintenanceCheck.basicComponent) === null || _maintenanceCheck_basicComponent === void 0 ? void 0 : _maintenanceCheck_basicComponent.id) || 0, vessels);\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"hidden lg:block\",\n                        children: ((_maintenanceCheck_basicComponent1 = maintenanceCheck.basicComponent) === null || _maintenanceCheck_basicComponent1 === void 0 ? void 0 : _maintenanceCheck_basicComponent1.id) > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2.5\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_16__.Avatar, {\n                                    size: \"sm\",\n                                    variant: \"secondary\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_16__.AvatarFallback, {\n                                        className: \"text-xs\",\n                                        children: getVesselInitials((vesselDetails === null || vesselDetails === void 0 ? void 0 : vesselDetails.title) || maintenanceCheck.basicComponent.title || undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                        lineNumber: 290,\n                                        columnNumber: 47\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                    lineNumber: 289,\n                                    columnNumber: 43\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    href: \"/vessel/info?id=\".concat(maintenanceCheck.basicComponent.id),\n                                    className: \"hover:underline\",\n                                    children: maintenanceCheck.basicComponent.title\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                    lineNumber: 300,\n                                    columnNumber: 43\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                            lineNumber: 288,\n                            columnNumber: 39\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                        lineNumber: 286,\n                        columnNumber: 31\n                    }, this);\n                },\n                sortingFn: (rowA, rowB)=>{\n                    var _rowA_original_basicComponent, _rowA_original, _rowB_original_basicComponent, _rowB_original;\n                    const valueA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : (_rowA_original_basicComponent = _rowA_original.basicComponent) === null || _rowA_original_basicComponent === void 0 ? void 0 : _rowA_original_basicComponent.title) || \"\";\n                    const valueB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : (_rowB_original_basicComponent = _rowB_original.basicComponent) === null || _rowB_original_basicComponent === void 0 ? void 0 : _rowB_original_basicComponent.title) || \"\";\n                    return valueA.localeCompare(valueB);\n                }\n            }\n        ],\n        {\n            accessorKey: \"assigned\",\n            header: (param)=>{\n                let { column } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_13__.DataTableSortHeader, {\n                    column: column,\n                    title: \"Assigned to\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                    lineNumber: 328,\n                    columnNumber: 17\n                }, this);\n            },\n            cellAlignment: \"left\",\n            breakpoint: \"tablet-sm\",\n            cell: (param)=>{\n                let { row } = param;\n                var _maintenanceCheck_assignedTo, _maintenanceCheck_assignedTo1, _maintenanceCheck_assignedTo2;\n                const maintenanceCheck = row.original;\n                const crewDetails = getCrewDetails(((_maintenanceCheck_assignedTo = maintenanceCheck.assignedTo) === null || _maintenanceCheck_assignedTo === void 0 ? void 0 : _maintenanceCheck_assignedTo.id) || 0, crewInfo);\n                // Temporary debug log to verify the fix\n                if ((_maintenanceCheck_assignedTo1 = maintenanceCheck.assignedTo) === null || _maintenanceCheck_assignedTo1 === void 0 ? void 0 : _maintenanceCheck_assignedTo1.id) {\n                    console.log(\"✅ Crew lookup:\", {\n                        assignedToId: maintenanceCheck.assignedTo.id,\n                        crewFound: !!crewDetails,\n                        crewName: crewDetails ? \"\".concat(crewDetails.firstName, \" \").concat(crewDetails.surname) : \"Not found\"\n                    });\n                }\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"hidden md:block\",\n                    children: ((_maintenanceCheck_assignedTo2 = maintenanceCheck.assignedTo) === null || _maintenanceCheck_assignedTo2 === void 0 ? void 0 : _maintenanceCheck_assignedTo2.id) > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2.5\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_16__.Avatar, {\n                                className: \"h-8 w-8\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_16__.AvatarFallback, {\n                                    className: \"text-xs\",\n                                    children: getCrewInitials(crewDetails === null || crewDetails === void 0 ? void 0 : crewDetails.firstName, crewDetails === null || crewDetails === void 0 ? void 0 : crewDetails.surname)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                    lineNumber: 355,\n                                    columnNumber: 37\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                lineNumber: 354,\n                                columnNumber: 33\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                href: \"/crew/info?id=\".concat(maintenanceCheck.assignedTo.id),\n                                className: \"hover:underline hidden lg:block\",\n                                children: maintenanceCheck.assignedTo.name\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                lineNumber: 362,\n                                columnNumber: 33\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                        lineNumber: 353,\n                        columnNumber: 29\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                    lineNumber: 351,\n                    columnNumber: 21\n                }, this);\n            },\n            sortingFn: (rowA, rowB)=>{\n                var _rowA_original_assignedTo, _rowA_original, _rowB_original_assignedTo, _rowB_original;\n                const valueA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : (_rowA_original_assignedTo = _rowA_original.assignedTo) === null || _rowA_original_assignedTo === void 0 ? void 0 : _rowA_original_assignedTo.name) || \"\";\n                const valueB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : (_rowB_original_assignedTo = _rowB_original.assignedTo) === null || _rowB_original_assignedTo === void 0 ? void 0 : _rowB_original_assignedTo.name) || \"\";\n                return valueA.localeCompare(valueB);\n            }\n        },\n        {\n            accessorKey: \"inventory\",\n            header: \"Inventory item\",\n            cellAlignment: \"left\",\n            breakpoint: \"tablet-lg\",\n            cell: (param)=>{\n                let { row } = param;\n                var _maintenanceCheck_inventory;\n                const maintenanceCheck = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"hidden md:block\",\n                    children: ((_maintenanceCheck_inventory = maintenanceCheck.inventory) === null || _maintenanceCheck_inventory === void 0 ? void 0 : _maintenanceCheck_inventory.id) > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        href: \"/inventory/view?id=\".concat(maintenanceCheck.inventory.id),\n                        className: \"hover:underline\",\n                        children: maintenanceCheck.inventory.item\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                        lineNumber: 391,\n                        columnNumber: 29\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                    lineNumber: 389,\n                    columnNumber: 21\n                }, this);\n            }\n        },\n        {\n            accessorKey: \"status\",\n            header: (param)=>{\n                let { column } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_13__.DataTableSortHeader, {\n                    column: column,\n                    title: \"Status\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                    lineNumber: 404,\n                    columnNumber: 17\n                }, this);\n            },\n            cellAlignment: \"right\",\n            cell: (param)=>{\n                let { row } = param;\n                const maintenanceCheck = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"hidden md:block\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatusBadge, {\n                        maintenanceCheck: maintenanceCheck\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                        lineNumber: 411,\n                        columnNumber: 25\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                    lineNumber: 410,\n                    columnNumber: 21\n                }, this);\n            },\n            sortingFn: (rowA, rowB)=>{\n                var _rowA_original_isOverDue, _rowA_original, _rowB_original_isOverDue, _rowB_original;\n                const valueA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : (_rowA_original_isOverDue = _rowA_original.isOverDue) === null || _rowA_original_isOverDue === void 0 ? void 0 : _rowA_original_isOverDue.days) || \"\";\n                const valueB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : (_rowB_original_isOverDue = _rowB_original.isOverDue) === null || _rowB_original_isOverDue === void 0 ? void 0 : _rowB_original_isOverDue.days) || \"\";\n                return valueA.localeCompare(valueB);\n            }\n        }\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_filteredTable__WEBPACK_IMPORTED_MODULE_12__.DataTable, {\n        columns: columns,\n        data: maintenanceChecks,\n        pageSize: 20,\n        showToolbar: false\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n        lineNumber: 427,\n        columnNumber: 9\n    }, this);\n}\n_s(MaintenanceTable, \"Zp7KzlBBuyYf0TXkH/rj/yT9nuA=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_7__.usePathname,\n        next_navigation__WEBPACK_IMPORTED_MODULE_7__.useSearchParams,\n        _app_lib_vessel_icon_helper__WEBPACK_IMPORTED_MODULE_19__.useVesselIconData\n    ];\n});\n_c1 = MaintenanceTable;\nfunction TaskList() {\n    _s1();\n    var _s = $RefreshSig$();\n    const [maintenanceChecks, setMaintenanceChecks] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    const [filteredMaintenanceChecks, setFilteredMaintenanceChecks] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    const [vessels, setVessels] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    const [crewInfo, setCrewInfo] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    const [filter, setFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [keywordFilter, setKeywordFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [permissions, setPermissions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [edit_task, setEdit_task] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_7__.usePathname)();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_7__.useSearchParams)();\n    const { getVesselWithIcon } = (0,_app_lib_vessel_icon_helper__WEBPACK_IMPORTED_MODULE_19__.useVesselIconData)();\n    const bp = (0,_components_hooks_useBreakpoints__WEBPACK_IMPORTED_MODULE_20__.useBreakpoints)();\n    const init_permissions = ()=>{\n        if (permissions) {\n            if ((0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_8__.hasPermission)(\"EDIT_TASK\", permissions)) {\n                setEdit_task(true);\n            } else {\n                setEdit_task(false);\n            }\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setPermissions(_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_8__.getPermissions);\n        init_permissions();\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (permissions) {\n            init_permissions();\n        }\n    }, [\n        permissions\n    ]);\n    const [queryMaintenanceChecks] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_26__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_2__.GET_MAINTENANCE_CHECK_LIST, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            const data = response.readComponentMaintenanceCheckList[0].list;\n            if (data) {\n                handleSetMaintenanceChecks(data);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"queryMaintenanceChecks error\", error);\n        }\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isLoading) {\n            loadMaintenanceChecks();\n            setIsLoading(false);\n        }\n    }, [\n        isLoading\n    ]);\n    const loadMaintenanceChecks = async ()=>{\n        await queryMaintenanceChecks({\n            variables: {\n                inventoryID: 0,\n                vesselID: 0\n            }\n        });\n    };\n    const handleSetVessels = (vessels)=>{\n        const activeVessels = vessels.filter((vessel)=>!vessel.archived);\n        const appendedData = activeVessels.map((item)=>({\n                ...item\n            }));\n        appendedData.push({\n            title: \"Other\",\n            id: 0\n        });\n        setVessels(appendedData);\n    };\n    const getVesselList = function(handleSetVessels) {\n        let offline = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : false;\n        _s();\n        const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n        const vesselModel = new _app_offline_models_vessel__WEBPACK_IMPORTED_MODULE_24__[\"default\"]();\n        (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n            if (isLoading) {\n                loadVessels();\n                setIsLoading(false);\n            }\n        }, [\n            isLoading\n        ]);\n        const [queryVessels] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_26__.useLazyQuery)(_queries__WEBPACK_IMPORTED_MODULE_23__.ReadVessels, {\n            fetchPolicy: \"cache-and-network\",\n            onCompleted: (queryVesselResponse)=>{\n                if (queryVesselResponse.readVessels.nodes) {\n                    handleSetVessels(queryVesselResponse.readVessels.nodes);\n                }\n            },\n            onError: (error)=>{\n                console.error(\"queryVessels error\", error);\n            }\n        });\n        const loadVessels = async ()=>{\n            if (offline) {\n                const response = await vesselModel.getAll();\n                handleSetVessels(response);\n            } else {\n                await queryVessels({\n                    variables: {\n                        limit: 200,\n                        offset: 0\n                    }\n                });\n            }\n        };\n    };\n    _s(getVesselList, \"pQOK42e7v9ItR64U9CP/qx2cQME=\", false, function() {\n        return [\n            _apollo_client__WEBPACK_IMPORTED_MODULE_26__.useLazyQuery\n        ];\n    });\n    getVesselList(handleSetVessels);\n    const handleSetMaintenanceChecks = (tasks)=>{\n        setMaintenanceChecks(tasks);\n        setFilteredMaintenanceChecks(tasks);\n        const appendedData = Array.from(new Set(tasks.filter((item)=>item.assignedTo.id > 0).map((item)=>item.assignedTo.id)));\n        loadCrewMemberInfo(appendedData);\n    };\n    const [queryCrewMemberInfo] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_26__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_2__.GET_CREW_BY_IDS, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            const data = response.readSeaLogsMembers.nodes;\n            if (data) {\n                setCrewInfo(data);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"queryCrewMemberInfo error\", error);\n        }\n    });\n    const loadCrewMemberInfo = async (crewId)=>{\n        await queryCrewMemberInfo({\n            variables: {\n                crewMemberIDs: crewId.length > 0 ? crewId : [\n                    0\n                ]\n            }\n        });\n    };\n    const handleFilterOnChange = (param)=>{\n        let { type, data } = param;\n        const searchFilter = {\n            ...filter\n        };\n        let filteredTasks = maintenanceChecks || [];\n        // Vessel filter\n        if (type === \"vessel\") {\n            if (Array.isArray(data) && data.length > 0) {\n                searchFilter.basicComponentID = {\n                    in: data.map((item)=>+item.value)\n                };\n            } else if (data && !Array.isArray(data)) {\n                searchFilter.basicComponentID = {\n                    eq: +data.value\n                };\n            } else {\n                delete searchFilter.basicComponentID;\n            }\n        }\n        // Status filter\n        if (type === \"status\") {\n            if (Array.isArray(data) && data.length > 0) {\n                searchFilter.status = {\n                    in: data.map((item)=>item.value)\n                };\n            } else if (data && !Array.isArray(data)) {\n                searchFilter.status = {\n                    eq: data.value\n                };\n            } else {\n                delete searchFilter.status;\n            }\n        }\n        // Assigned member filter\n        if (type === \"member\") {\n            if (Array.isArray(data) && data.length > 0) {\n                searchFilter.assignedToID = {\n                    in: data.map((item)=>+item.value)\n                };\n            } else if (data && !Array.isArray(data)) {\n                searchFilter.assignedToID = {\n                    eq: +data.value\n                };\n            } else {\n                delete searchFilter.assignedToID;\n            }\n        }\n        // Date range\n        if (type === \"dateRange\") {\n            if ((data === null || data === void 0 ? void 0 : data.startDate) && (data === null || data === void 0 ? void 0 : data.endDate)) {\n                searchFilter.expires = {\n                    gte: data.startDate,\n                    lte: data.endDate\n                };\n            } else {\n                delete searchFilter.expires;\n            }\n        }\n        // Category\n        if (type === \"category\") {\n            if (Array.isArray(data) && data.length > 0) {\n                searchFilter.maintenanceCategoryID = {\n                    in: data.map((item)=>+item.value)\n                };\n            } else if (data && !Array.isArray(data)) {\n                searchFilter.maintenanceCategoryID = {\n                    eq: +data.value\n                };\n            } else {\n                delete searchFilter.maintenanceCategoryID;\n            }\n        }\n        // Recurring filter - handle client-side filtering\n        let recurringFilter = null;\n        if (type === \"recurring\") {\n            if (data && !Array.isArray(data)) {\n                recurringFilter = data.value;\n            }\n        }\n        // Keyword filter\n        let keyFilter = keywordFilter;\n        if (type === \"keyword\" || keyFilter && keyFilter.length > 0) {\n            var _data_value;\n            const keyword = data === null || data === void 0 ? void 0 : (_data_value = data.value) === null || _data_value === void 0 ? void 0 : _data_value.trim().toLowerCase();\n            if (keyword && keyword.length > 0) {\n                filteredTasks = filteredTasks.filter((maintenanceCheck)=>[\n                        maintenanceCheck.name,\n                        maintenanceCheck.comments,\n                        maintenanceCheck.workOrderNumber\n                    ].some((field)=>field === null || field === void 0 ? void 0 : field.toLowerCase().includes(keyword)));\n                keyFilter = data.value;\n            } else {\n                keyFilter = null;\n            }\n        }\n        // Filtering based on current searchFilter\n        // Filter by vessel (basicComponentID)\n        if (searchFilter.basicComponentID) {\n            const ids = searchFilter.basicComponentID.in || [\n                searchFilter.basicComponentID.eq\n            ];\n            filteredTasks = filteredTasks.filter((mc)=>{\n                var _mc_basicComponent;\n                return ids.includes((_mc_basicComponent = mc.basicComponent) === null || _mc_basicComponent === void 0 ? void 0 : _mc_basicComponent.id);\n            });\n        }\n        // Filter by status\n        if (searchFilter.status) {\n            const statuses = searchFilter.status.in || [\n                searchFilter.status.eq\n            ];\n            filteredTasks = filteredTasks.filter((mc)=>statuses.includes(mc.status));\n        }\n        // Filter by assignedToID\n        if (searchFilter.assignedToID) {\n            const ids = searchFilter.assignedToID.in || [\n                searchFilter.assignedToID.eq\n            ];\n            filteredTasks = filteredTasks.filter((mc)=>{\n                var _mc_assignedTo;\n                return ids.includes((_mc_assignedTo = mc.assignedTo) === null || _mc_assignedTo === void 0 ? void 0 : _mc_assignedTo.id);\n            });\n        }\n        // Filter by category\n        if (searchFilter.maintenanceCategoryID) {\n            const ids = searchFilter.maintenanceCategoryID.in || [\n                searchFilter.maintenanceCategoryID.eq\n            ];\n            filteredTasks = filteredTasks.filter((mc)=>ids.includes(mc.maintenanceCategoryID));\n        }\n        // Filter by date range\n        if (searchFilter.expires && searchFilter.expires.gte && searchFilter.expires.lte) {\n            filteredTasks = filteredTasks.filter((mc)=>dayjs__WEBPACK_IMPORTED_MODULE_5___default()(mc.startDate).isAfter(dayjs__WEBPACK_IMPORTED_MODULE_5___default()(searchFilter.expires.gte)) && dayjs__WEBPACK_IMPORTED_MODULE_5___default()(mc.startDate).isBefore(dayjs__WEBPACK_IMPORTED_MODULE_5___default()(searchFilter.expires.lte)));\n        }\n        // Filter by recurring status\n        if (recurringFilter) {\n            if (recurringFilter === \"recurring\") {\n                // Recurring tasks have recurringID > 0\n                filteredTasks = filteredTasks.filter((mc)=>mc.recurringID > 0);\n            } else if (recurringFilter === \"one-off\") {\n                // One-off tasks have recurringID = 0 or null\n                filteredTasks = filteredTasks.filter((mc)=>!mc.recurringID || mc.recurringID === 0);\n            }\n        }\n        // Set updated filters\n        setFilter(searchFilter);\n        setKeywordFilter(keyFilter);\n        setFilteredMaintenanceChecks(filteredTasks);\n    // Optionally call API: loadMaintenanceChecks(searchFilter, keyFilter);\n    };\n    const downloadCsv = ()=>{\n        if (!maintenanceChecks || !vessels) {\n            return;\n        }\n        const csvEntries = [\n            [\n                \"task\",\n                \"location\",\n                \"assigned to\",\n                \"due\"\n            ]\n        ];\n        maintenanceChecks.filter((maintenanceCheck)=>maintenanceCheck.status !== \"Save_As_Draft\").forEach((maintenanceCheck)=>{\n            var _maintenanceCheck_assignedTo, _maintenanceCheck_assignedTo1;\n            const crewDetails = getCrewDetails(((_maintenanceCheck_assignedTo = maintenanceCheck.assignedTo) === null || _maintenanceCheck_assignedTo === void 0 ? void 0 : _maintenanceCheck_assignedTo.id) || 0, crewInfo);\n            const assignedToName = crewDetails ? \"\".concat(crewDetails.firstName, \" \").concat(crewDetails.surname) : ((_maintenanceCheck_assignedTo1 = maintenanceCheck.assignedTo) === null || _maintenanceCheck_assignedTo1 === void 0 ? void 0 : _maintenanceCheck_assignedTo1.name) || \"\";\n            csvEntries.push([\n                maintenanceCheck.name,\n                (vessels === null || vessels === void 0 ? void 0 : vessels.filter((vessel)=>{\n                    var _maintenanceCheck_basicComponent;\n                    return (vessel === null || vessel === void 0 ? void 0 : vessel.id) == ((_maintenanceCheck_basicComponent = maintenanceCheck.basicComponent) === null || _maintenanceCheck_basicComponent === void 0 ? void 0 : _maintenanceCheck_basicComponent.id);\n                }).map((vessel)=>vessel.title).join(\", \")) || \"\",\n                assignedToName,\n                (0,_reporting_maintenance_status_activity_report__WEBPACK_IMPORTED_MODULE_9__.dueStatusLabel)(maintenanceCheck.isOverDue)\n            ]);\n        });\n        (0,_app_helpers_csvHelper__WEBPACK_IMPORTED_MODULE_10__.exportCsv)(csvEntries);\n    };\n    const downloadPdf = ()=>{\n        if (!maintenanceChecks || !vessels) {\n            return;\n        }\n        const headers = [\n            [\n                \"Task Name\",\n                \"Location\",\n                \"Assigned To\",\n                \"Due\"\n            ]\n        ];\n        const body = maintenanceChecks.filter((maintenanceCheck)=>maintenanceCheck.status !== \"Save_As_Draft\").map((maintenanceCheck)=>{\n            var _maintenanceCheck_assignedTo, _maintenanceCheck_assignedTo1;\n            const crewDetails = getCrewDetails(((_maintenanceCheck_assignedTo = maintenanceCheck.assignedTo) === null || _maintenanceCheck_assignedTo === void 0 ? void 0 : _maintenanceCheck_assignedTo.id) || 0, crewInfo);\n            const assignedToName = crewDetails ? \"\".concat(crewDetails.firstName, \" \").concat(crewDetails.surname) : ((_maintenanceCheck_assignedTo1 = maintenanceCheck.assignedTo) === null || _maintenanceCheck_assignedTo1 === void 0 ? void 0 : _maintenanceCheck_assignedTo1.name) || \"\";\n            return [\n                maintenanceCheck.name,\n                (vessels === null || vessels === void 0 ? void 0 : vessels.filter((vessel)=>{\n                    var _maintenanceCheck_basicComponent;\n                    return (vessel === null || vessel === void 0 ? void 0 : vessel.id) == ((_maintenanceCheck_basicComponent = maintenanceCheck.basicComponent) === null || _maintenanceCheck_basicComponent === void 0 ? void 0 : _maintenanceCheck_basicComponent.id);\n                }).map((vessel)=>vessel.title).join(\", \")) || \"\",\n                assignedToName,\n                (0,_reporting_maintenance_status_activity_report__WEBPACK_IMPORTED_MODULE_9__.dueStatusLabel)(maintenanceCheck.isOverDue)\n            ];\n        });\n        (0,_app_helpers_pdfHelper__WEBPACK_IMPORTED_MODULE_11__.exportPdfTable)({\n            headers,\n            body\n        });\n    };\n    const createMaintenanceColumns = (crewInfo, getVesselWithIcon)=>(0,_components_filteredTable__WEBPACK_IMPORTED_MODULE_12__.createColumns)([\n            {\n                accessorKey: \"title\",\n                header: (param)=>{\n                    let { column } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_13__.DataTableSortHeader, {\n                        column: column,\n                        title: \"Title\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                        lineNumber: 844,\n                        columnNumber: 21\n                    }, this);\n                },\n                cell: (param)=>{\n                    let { row } = param;\n                    var _maintenanceCheck_assignedTo, _maintenanceCheck_isOverDue, _maintenanceCheck_isOverDue1, _maintenanceCheck_inventory, _maintenanceCheck_assignedTo1, _maintenanceCheck_basicComponent;\n                    const maintenanceCheck = row.original;\n                    const crewDetails = getCrewDetails(((_maintenanceCheck_assignedTo = maintenanceCheck.assignedTo) === null || _maintenanceCheck_assignedTo === void 0 ? void 0 : _maintenanceCheck_assignedTo.id) || 0, crewInfo);\n                    const overDueStatus = (_maintenanceCheck_isOverDue = maintenanceCheck.isOverDue) === null || _maintenanceCheck_isOverDue === void 0 ? void 0 : _maintenanceCheck_isOverDue.status;\n                    const overDueDays = (_maintenanceCheck_isOverDue1 = maintenanceCheck.isOverDue) === null || _maintenanceCheck_isOverDue1 === void 0 ? void 0 : _maintenanceCheck_isOverDue1.day;\n                    var _maintenanceCheck_name, _maintenanceCheck_name1;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_21__.Card, {\n                                className: \"xs:hidden space-y-3 p-2.5 min-h-20 w-full shadow-none rounded-none bg-transparent\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_21__.CardContent, {\n                                    className: \"p-0 space-y-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                            href: \"/maintenance?taskID=\".concat(maintenanceCheck.id, \"&redirect_to=\").concat(pathname, \"?\").concat(searchParams.toString()),\n                                            className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_25__.cn)(\"text-base\", overDueStatus === \"High\" ? \"text-cinnabar-500 hover:text-cinnabar-700\" : overDueStatus === \"Upcoming\" ? \"text-fire-bush-500 hover:text-fire-bush-700\" : \"hover:text-curious-blue-400\"),\n                                            children: [\n                                                (_maintenanceCheck_name = maintenanceCheck.name) !== null && _maintenanceCheck_name !== void 0 ? _maintenanceCheck_name : \"Task #\".concat(maintenanceCheck.id, \" (No Name) - \").concat(dayjs__WEBPACK_IMPORTED_MODULE_5___default()(maintenanceCheck.created).format(\"DD/MM/YYYY\")),\n                                                \" \"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                            lineNumber: 862,\n                                            columnNumber: 37\n                                        }, this),\n                                        ((_maintenanceCheck_inventory = maintenanceCheck.inventory) === null || _maintenanceCheck_inventory === void 0 ? void 0 : _maintenanceCheck_inventory.id) > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-col\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xs text-muted-foreground\",\n                                                    children: \"Inventory item\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                                    lineNumber: 881,\n                                                    columnNumber: 45\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                    href: \"/inventory/view?id=\".concat(maintenanceCheck.inventory.id),\n                                                    className: \"hover:underline text-sm\",\n                                                    children: maintenanceCheck.inventory.item\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                                    lineNumber: 884,\n                                                    columnNumber: 45\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                            lineNumber: 880,\n                                            columnNumber: 41\n                                        }, this),\n                                        ((_maintenanceCheck_assignedTo1 = maintenanceCheck.assignedTo) === null || _maintenanceCheck_assignedTo1 === void 0 ? void 0 : _maintenanceCheck_assignedTo1.id) > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_16__.Avatar, {\n                                            variant: \"secondary\",\n                                            className: \"h-8 w-8\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_16__.AvatarFallback, {\n                                                className: \"text-xs\",\n                                                children: getCrewInitials(crewDetails === null || crewDetails === void 0 ? void 0 : crewDetails.firstName, crewDetails === null || crewDetails === void 0 ? void 0 : crewDetails.surname)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                                lineNumber: 900,\n                                                columnNumber: 45\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                            lineNumber: 897,\n                                            columnNumber: 41\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                    lineNumber: 860,\n                                    columnNumber: 33\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                lineNumber: 859,\n                                columnNumber: 29\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"hidden xs:block\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center flex-nowrap gap-2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                            href: \"/maintenance?taskID=\".concat(maintenanceCheck.id, \"&redirect_to=\").concat(pathname, \"?\").concat(searchParams.toString()),\n                                            className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_25__.cn)(\"text-base\", overDueStatus === \"High\" ? \"text-cinnabar-500 hover:text-cinnabar-700\" : overDueStatus === \"Upcoming\" ? \"text-fire-bush-500 hover:text-fire-bush-700\" : \"hover:text-curious-blue-400\"),\n                                            children: (_maintenanceCheck_name1 = maintenanceCheck.name) !== null && _maintenanceCheck_name1 !== void 0 ? _maintenanceCheck_name1 : \"Task #\".concat(maintenanceCheck.id, \" (No Name) - \").concat(dayjs__WEBPACK_IMPORTED_MODULE_5___default()(maintenanceCheck.created).format(\"DD/MM/YYYY\"))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                            lineNumber: 914,\n                                            columnNumber: 37\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                        lineNumber: 913,\n                                        columnNumber: 33\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"md:hidden\",\n                                        children: ((_maintenanceCheck_basicComponent = maintenanceCheck.basicComponent) === null || _maintenanceCheck_basicComponent === void 0 ? void 0 : _maintenanceCheck_basicComponent.id) > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                            href: \"/vessel/info?id=\".concat(maintenanceCheck.basicComponent.id),\n                                            className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_25__.cn)(\"text-base\", overDueStatus === \"High\" ? \"text-cinnabar-500 hover:text-cinnabar-700\" : overDueStatus === \"Upcoming\" ? \"text-fire-bush-500 hover:text-fire-bush-700\" : \"hover:text-curious-blue-400\"),\n                                            children: maintenanceCheck.basicComponent.title\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                            lineNumber: 934,\n                                            columnNumber: 41\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                        lineNumber: 931,\n                                        columnNumber: 33\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                lineNumber: 912,\n                                columnNumber: 29\n                            }, this)\n                        ]\n                    }, void 0, true);\n                },\n                sortingFn: (rowA, rowB)=>{\n                    var _rowA_original, _rowB_original;\n                    const valueA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : _rowA_original.name) || \"\";\n                    const valueB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : _rowB_original.name) || \"\";\n                    return valueA.localeCompare(valueB);\n                }\n            },\n            {\n                accessorKey: \"location\",\n                header: (param)=>{\n                    let { column } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_13__.DataTableSortHeader, {\n                        column: column,\n                        title: \"Location\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                        lineNumber: 968,\n                        columnNumber: 21\n                    }, this);\n                },\n                cellAlignment: \"left\",\n                breakpoint: \"laptop\",\n                cell: (param)=>{\n                    let { row } = param;\n                    var _maintenanceCheck_basicComponent, _maintenanceCheck_basicComponent1, _maintenanceCheck_basicComponent2;\n                    const maintenanceCheck = row.original;\n                    const vesselDetails = getVesselDetails(((_maintenanceCheck_basicComponent = maintenanceCheck.basicComponent) === null || _maintenanceCheck_basicComponent === void 0 ? void 0 : _maintenanceCheck_basicComponent.id) || 0, vessels);\n                    // Get vessel with icon data\n                    const vesselWithIcon = getVesselWithIcon(((_maintenanceCheck_basicComponent1 = maintenanceCheck.basicComponent) === null || _maintenanceCheck_basicComponent1 === void 0 ? void 0 : _maintenanceCheck_basicComponent1.id) || 0, vesselDetails);\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: ((_maintenanceCheck_basicComponent2 = maintenanceCheck.basicComponent) === null || _maintenanceCheck_basicComponent2 === void 0 ? void 0 : _maintenanceCheck_basicComponent2.id) > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2.5\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_17__.Tooltip, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_17__.TooltipTrigger, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"min-w-fit\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_vessels_vesel_icon__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                    vessel: vesselWithIcon\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                                    lineNumber: 992,\n                                                    columnNumber: 49\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                                lineNumber: 991,\n                                                columnNumber: 45\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                            lineNumber: 990,\n                                            columnNumber: 41\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_17__.TooltipContent, {\n                                            children: maintenanceCheck.basicComponent.title\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                            lineNumber: 997,\n                                            columnNumber: 41\n                                        }, this)\n                                    ]\n                                }, vesselDetails === null || vesselDetails === void 0 ? void 0 : vesselDetails.id, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                    lineNumber: 989,\n                                    columnNumber: 37\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    href: \"/vessel/info?id=\".concat(maintenanceCheck.basicComponent.id),\n                                    className: \"hover:underline\",\n                                    children: maintenanceCheck.basicComponent.title\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                    lineNumber: 1004,\n                                    columnNumber: 37\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                            lineNumber: 988,\n                            columnNumber: 33\n                        }, this)\n                    }, void 0, false);\n                },\n                sortingFn: (rowA, rowB)=>{\n                    var _rowA_original_basicComponent, _rowA_original, _rowB_original_basicComponent, _rowB_original;\n                    const valueA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : (_rowA_original_basicComponent = _rowA_original.basicComponent) === null || _rowA_original_basicComponent === void 0 ? void 0 : _rowA_original_basicComponent.title) || \"\";\n                    const valueB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : (_rowB_original_basicComponent = _rowB_original.basicComponent) === null || _rowB_original_basicComponent === void 0 ? void 0 : _rowB_original_basicComponent.title) || \"\";\n                    return valueA.localeCompare(valueB);\n                }\n            },\n            {\n                accessorKey: \"assigned\",\n                header: (param)=>{\n                    let { column } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_13__.DataTableSortHeader, {\n                        column: column,\n                        title: \"Assigned to\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                        lineNumber: 1026,\n                        columnNumber: 21\n                    }, this);\n                },\n                cellAlignment: \"left\",\n                breakpoint: \"tablet-md\",\n                cell: (param)=>{\n                    let { row } = param;\n                    var _maintenanceCheck_assignedTo, _maintenanceCheck_assignedTo1;\n                    const maintenanceCheck = row.original;\n                    const crewDetails = getCrewDetails(((_maintenanceCheck_assignedTo = maintenanceCheck.assignedTo) === null || _maintenanceCheck_assignedTo === void 0 ? void 0 : _maintenanceCheck_assignedTo.id) || 0, crewInfo);\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: ((_maintenanceCheck_assignedTo1 = maintenanceCheck.assignedTo) === null || _maintenanceCheck_assignedTo1 === void 0 ? void 0 : _maintenanceCheck_assignedTo1.id) > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2.5\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_16__.Avatar, {\n                                    variant: \"secondary\",\n                                    className: \"h-8 w-8\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_16__.AvatarFallback, {\n                                        className: \"text-xs\",\n                                        children: getCrewInitials(crewDetails === null || crewDetails === void 0 ? void 0 : crewDetails.firstName, crewDetails === null || crewDetails === void 0 ? void 0 : crewDetails.surname)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                        lineNumber: 1044,\n                                        columnNumber: 41\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                    lineNumber: 1041,\n                                    columnNumber: 37\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    href: \"/crew/info?id=\".concat(maintenanceCheck.assignedTo.id),\n                                    className: \"hover:underline hidden tablet-md:block\",\n                                    children: maintenanceCheck.assignedTo.name\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                    lineNumber: 1051,\n                                    columnNumber: 37\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                            lineNumber: 1040,\n                            columnNumber: 33\n                        }, this)\n                    }, void 0, false);\n                },\n                sortingFn: (rowA, rowB)=>{\n                    var _rowA_original_assignedTo, _rowA_original, _rowB_original_assignedTo, _rowB_original;\n                    const valueA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : (_rowA_original_assignedTo = _rowA_original.assignedTo) === null || _rowA_original_assignedTo === void 0 ? void 0 : _rowA_original_assignedTo.name) || \"\";\n                    const valueB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : (_rowB_original_assignedTo = _rowB_original.assignedTo) === null || _rowB_original_assignedTo === void 0 ? void 0 : _rowB_original_assignedTo.name) || \"\";\n                    return valueA.localeCompare(valueB);\n                }\n            },\n            {\n                accessorKey: \"inventory\",\n                header: \"Inventory item\",\n                cellAlignment: \"left\",\n                breakpoint: \"phablet\",\n                cell: (param)=>{\n                    let { row } = param;\n                    var _maintenanceCheck_inventory;\n                    const maintenanceCheck = row.original;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: ((_maintenanceCheck_inventory = maintenanceCheck.inventory) === null || _maintenanceCheck_inventory === void 0 ? void 0 : _maintenanceCheck_inventory.id) > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            href: \"/inventory/view?id=\".concat(maintenanceCheck.inventory.id),\n                            className: \"hover:underline\",\n                            children: maintenanceCheck.inventory.item\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                            lineNumber: 1080,\n                            columnNumber: 33\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: \"-\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                            lineNumber: 1086,\n                            columnNumber: 33\n                        }, this)\n                    }, void 0, false);\n                }\n            },\n            {\n                accessorKey: \"status\",\n                header: (param)=>{\n                    let { column } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_13__.DataTableSortHeader, {\n                        column: column,\n                        title: \"Status\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                        lineNumber: 1095,\n                        columnNumber: 21\n                    }, this);\n                },\n                cellAlignment: \"right\",\n                cell: (param)=>{\n                    let { row } = param;\n                    var _maintenanceCheck_isOverDue, _maintenanceCheck_isOverDue1;\n                    const maintenanceCheck = row.original;\n                    if (!maintenanceCheck) {\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: \"-\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                            lineNumber: 1102,\n                            columnNumber: 32\n                        }, this);\n                    }\n                    const overDueStatus = (_maintenanceCheck_isOverDue = maintenanceCheck.isOverDue) === null || _maintenanceCheck_isOverDue === void 0 ? void 0 : _maintenanceCheck_isOverDue.status;\n                    const overDueDays = (_maintenanceCheck_isOverDue1 = maintenanceCheck.isOverDue) === null || _maintenanceCheck_isOverDue1 === void 0 ? void 0 : _maintenanceCheck_isOverDue1.day;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: overDueStatus === \"High\" ? !bp[\"tablet-lg\"] ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \" items-end mr-2.5 text-nowrap w-fit\\n                                                    \".concat(overDueStatus === \"High\" ? \"alert text-sm !px-1.5 !py-0.5 xs:px-3 xs:py-1 whitespace-nowrap\" : \"\", \"\\n                                                    \"),\n                            children: overDueDays * -1 + \" days ago\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                            lineNumber: 1112,\n                            columnNumber: 37\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatusBadge, {\n                            maintenanceCheck: maintenanceCheck\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                            lineNumber: 1119,\n                            columnNumber: 37\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatusBadge, {\n                            maintenanceCheck: maintenanceCheck\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                            lineNumber: 1124,\n                            columnNumber: 33\n                        }, this)\n                    }, void 0, false);\n                },\n                sortingFn: (rowA, rowB)=>{\n                    var _rowA_original_isOverDue, _rowA_original, _rowB_original_isOverDue, _rowB_original;\n                    const valueA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : (_rowA_original_isOverDue = _rowA_original.isOverDue) === null || _rowA_original_isOverDue === void 0 ? void 0 : _rowA_original_isOverDue.days) || \"\";\n                    const valueB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : (_rowB_original_isOverDue = _rowB_original.isOverDue) === null || _rowB_original_isOverDue === void 0 ? void 0 : _rowB_original_isOverDue.days) || \"\";\n                    return valueA.localeCompare(valueB);\n                }\n            }\n        ]);\n    const columns = createMaintenanceColumns(crewInfo || [], getVesselWithIcon);\n    // Row status evaluator for maintenance tasks\n    const getMaintenanceRowStatus = (maintenanceCheck)=>{\n        var _maintenanceCheck_isOverDue;\n        // Skip completed, archived, or draft tasks\n        if (maintenanceCheck.status === \"Completed\" || maintenanceCheck.archived || maintenanceCheck.status === \"Save_As_Draft\") {\n            return \"normal\";\n        }\n        const overDueStatus = (_maintenanceCheck_isOverDue = maintenanceCheck.isOverDue) === null || _maintenanceCheck_isOverDue === void 0 ? void 0 : _maintenanceCheck_isOverDue.status;\n        // Use the pre-calculated status values from the system\n        switch(overDueStatus){\n            case \"High\":\n                return \"overdue\" // Red highlighting\n                ;\n            case \"Upcoming\":\n                return \"upcoming\" // Orange highlighting\n                ;\n            case \"Medium\":\n            case \"Open\":\n            default:\n                return \"normal\" // No highlighting\n                ;\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_list_header__WEBPACK_IMPORTED_MODULE_15__.ListHeader, {\n                title: \"Maintenance\",\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_lib_icons__WEBPACK_IMPORTED_MODULE_22__.SealogsMaintenanceIcon, {\n                    className: \"h-12 w-12 ring-1 p-1 rounded-full bg-[#fff]\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                    lineNumber: 1177,\n                    columnNumber: 21\n                }, void 0),\n                actions: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_filter_components_maintenance_actions__WEBPACK_IMPORTED_MODULE_14__.MaintenanceFilterActions, {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                    lineNumber: 1179,\n                    columnNumber: 26\n                }, void 0),\n                titleClassName: \"\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                lineNumber: 1174,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-16\",\n                children: maintenanceChecks && vessels ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_filteredTable__WEBPACK_IMPORTED_MODULE_12__.DataTable, {\n                    columns: columns,\n                    data: filteredMaintenanceChecks || [],\n                    pageSize: 20,\n                    onChange: handleFilterOnChange,\n                    rowStatus: getMaintenanceRowStatus\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                    lineNumber: 1184,\n                    columnNumber: 21\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_skeletons__WEBPACK_IMPORTED_MODULE_3__.TableSkeleton, {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                    lineNumber: 1192,\n                    columnNumber: 21\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                lineNumber: 1182,\n                columnNumber: 13\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s1(TaskList, \"LFHio0/i4l3OQsePNWySacFXTG4=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_7__.usePathname,\n        next_navigation__WEBPACK_IMPORTED_MODULE_7__.useSearchParams,\n        _app_lib_vessel_icon_helper__WEBPACK_IMPORTED_MODULE_19__.useVesselIconData,\n        _components_hooks_useBreakpoints__WEBPACK_IMPORTED_MODULE_20__.useBreakpoints,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_26__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_26__.useLazyQuery\n    ];\n});\n_c2 = TaskList;\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"StatusBadge\");\n$RefreshReg$(_c1, \"MaintenanceTable\");\n$RefreshReg$(_c2, \"TaskList\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/maintenance/list/list.tsx\n"));

/***/ })

});