"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/maintenance/page",{

/***/ "(app-pages-browser)/./src/app/ui/maintenance/list/list.tsx":
/*!**********************************************!*\
  !*** ./src/app/ui/maintenance/list/list.tsx ***!
  \**********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MaintenanceTable: function() { return /* binding */ MaintenanceTable; },\n/* harmony export */   StatusBadge: function() { return /* binding */ StatusBadge; },\n/* harmony export */   \"default\": function() { return /* binding */ TaskList; },\n/* harmony export */   getVesselDetails: function() { return /* binding */ getVesselDetails; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/react/hooks/useLazyQuery.js\");\n/* harmony import */ var _app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/app/lib/graphQL/query */ \"(app-pages-browser)/./src/app/lib/graphQL/query/index.ts\");\n/* harmony import */ var _components_skeletons__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/skeletons */ \"(app-pages-browser)/./src/components/skeletons.tsx\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/api/link.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! dayjs */ \"(app-pages-browser)/./node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/dayjs.min.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(dayjs__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var lodash_isEmpty__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! lodash/isEmpty */ \"(app-pages-browser)/./node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/isEmpty.js\");\n/* harmony import */ var lodash_isEmpty__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(lodash_isEmpty__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/app/helpers/userHelper */ \"(app-pages-browser)/./src/app/helpers/userHelper.ts\");\n/* harmony import */ var _reporting_maintenance_status_activity_report__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../../reporting/maintenance-status-activity-report */ \"(app-pages-browser)/./src/app/ui/reporting/maintenance-status-activity-report.tsx\");\n/* harmony import */ var _app_helpers_csvHelper__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/app/helpers/csvHelper */ \"(app-pages-browser)/./src/app/helpers/csvHelper.ts\");\n/* harmony import */ var _app_helpers_pdfHelper__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/app/helpers/pdfHelper */ \"(app-pages-browser)/./src/app/helpers/pdfHelper.ts\");\n/* harmony import */ var _components_filteredTable__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/filteredTable */ \"(app-pages-browser)/./src/components/filteredTable.tsx\");\n/* harmony import */ var _components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/data-table-sort-header */ \"(app-pages-browser)/./src/components/data-table-sort-header.tsx\");\n/* harmony import */ var _components_filter_components_maintenance_actions__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/components/filter/components/maintenance-actions */ \"(app-pages-browser)/./src/components/filter/components/maintenance-actions.tsx\");\n/* harmony import */ var _components_ui_list_header__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/components/ui/list-header */ \"(app-pages-browser)/./src/components/ui/list-header.tsx\");\n/* harmony import */ var _components_ui_avatar__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/components/ui/avatar */ \"(app-pages-browser)/./src/components/ui/avatar.tsx\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.ts\");\n/* harmony import */ var _vessels_vesel_icon__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! ../../vessels/vesel-icon */ \"(app-pages-browser)/./src/app/ui/vessels/vesel-icon.tsx\");\n/* harmony import */ var _app_lib_vessel_icon_helper__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @/app/lib/vessel-icon-helper */ \"(app-pages-browser)/./src/app/lib/vessel-icon-helper.tsx\");\n/* harmony import */ var _components_hooks_useBreakpoints__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! @/components/hooks/useBreakpoints */ \"(app-pages-browser)/./src/components/hooks/useBreakpoints.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _app_lib_icons__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! @/app/lib/icons */ \"(app-pages-browser)/./src/app/lib/icons/index.ts\");\n/* harmony import */ var _queries__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! ./queries */ \"(app-pages-browser)/./src/app/ui/maintenance/list/queries.ts\");\n/* harmony import */ var _app_offline_models_vessel__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! @/app/offline/models/vessel */ \"(app-pages-browser)/./src/app/offline/models/vessel.js\");\n/* harmony import */ var _app_lib_utils__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! @/app/lib/utils */ \"(app-pages-browser)/./src/app/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ getVesselDetails,StatusBadge,MaintenanceTable,default auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// Helper functions\nconst getCrewInitials = (firstName, surname)=>{\n    var _firstName_charAt, _surname_charAt;\n    if (!firstName && !surname) return \"??\";\n    const first = (firstName === null || firstName === void 0 ? void 0 : (_firstName_charAt = firstName.charAt(0)) === null || _firstName_charAt === void 0 ? void 0 : _firstName_charAt.toUpperCase()) || \"\";\n    const last = (surname === null || surname === void 0 ? void 0 : (_surname_charAt = surname.charAt(0)) === null || _surname_charAt === void 0 ? void 0 : _surname_charAt.toUpperCase()) || \"\";\n    return \"\".concat(first).concat(last) || \"??\";\n};\nconst getVesselInitials = (title)=>{\n    if (!title) return \"??\";\n    const words = title.split(\" \").filter((word)=>word.length > 0);\n    if (words.length === 1) {\n        return words[0].substring(0, 2).toUpperCase();\n    }\n    return words.slice(0, 2).map((word)=>word.charAt(0).toUpperCase()).join(\"\");\n};\nconst getCrewDetails = (assignedToID, crewInfo)=>{\n    return crewInfo === null || crewInfo === void 0 ? void 0 : crewInfo.find((crew)=>crew.id === assignedToID.toString());\n};\nconst getVesselDetails = (vesselID, vessels)=>{\n    return vessels === null || vessels === void 0 ? void 0 : vessels.find((vessel)=>vessel.id === vesselID);\n};\n// Status badge component following UI standards\nconst StatusBadge = (param)=>{\n    let { maintenanceCheck } = param;\n    var _maintenanceCheck_isOverDue, _maintenanceCheck_isOverDue1, _maintenanceCheck_isOverDue2, _maintenanceCheck_isOverDue3, _maintenanceCheck_isOverDue4, _maintenanceCheck_isOverDue5, _maintenanceCheck_isOverDue6, _maintenanceCheck_isOverDue7, _maintenanceCheck_isOverDue8, _maintenanceCheck_isOverDue9;\n    const isOverdue = (maintenanceCheck === null || maintenanceCheck === void 0 ? void 0 : (_maintenanceCheck_isOverDue = maintenanceCheck.isOverDue) === null || _maintenanceCheck_isOverDue === void 0 ? void 0 : _maintenanceCheck_isOverDue.status) === \"High\";\n    // Get status text\n    let statusText = \"\";\n    if ((maintenanceCheck === null || maintenanceCheck === void 0 ? void 0 : (_maintenanceCheck_isOverDue1 = maintenanceCheck.isOverDue) === null || _maintenanceCheck_isOverDue1 === void 0 ? void 0 : _maintenanceCheck_isOverDue1.status) && [\n        \"High\",\n        \"Medium\",\n        \"Low\"\n    ].includes(maintenanceCheck.isOverDue.status)) {\n        var _maintenanceCheck_isOverDue10;\n        statusText = maintenanceCheck === null || maintenanceCheck === void 0 ? void 0 : (_maintenanceCheck_isOverDue10 = maintenanceCheck.isOverDue) === null || _maintenanceCheck_isOverDue10 === void 0 ? void 0 : _maintenanceCheck_isOverDue10.days;\n    } else if ((maintenanceCheck === null || maintenanceCheck === void 0 ? void 0 : (_maintenanceCheck_isOverDue2 = maintenanceCheck.isOverDue) === null || _maintenanceCheck_isOverDue2 === void 0 ? void 0 : _maintenanceCheck_isOverDue2.status) === \"Completed\" && (maintenanceCheck === null || maintenanceCheck === void 0 ? void 0 : (_maintenanceCheck_isOverDue3 = maintenanceCheck.isOverDue) === null || _maintenanceCheck_isOverDue3 === void 0 ? void 0 : _maintenanceCheck_isOverDue3.days) === \"Save As Draft\") {\n        var _maintenanceCheck_isOverDue11;\n        statusText = maintenanceCheck === null || maintenanceCheck === void 0 ? void 0 : (_maintenanceCheck_isOverDue11 = maintenanceCheck.isOverDue) === null || _maintenanceCheck_isOverDue11 === void 0 ? void 0 : _maintenanceCheck_isOverDue11.days;\n    } else if ((maintenanceCheck === null || maintenanceCheck === void 0 ? void 0 : (_maintenanceCheck_isOverDue4 = maintenanceCheck.isOverDue) === null || _maintenanceCheck_isOverDue4 === void 0 ? void 0 : _maintenanceCheck_isOverDue4.status) === \"Upcoming\") {\n        var _maintenanceCheck_isOverDue12;\n        statusText = maintenanceCheck === null || maintenanceCheck === void 0 ? void 0 : (_maintenanceCheck_isOverDue12 = maintenanceCheck.isOverDue) === null || _maintenanceCheck_isOverDue12 === void 0 ? void 0 : _maintenanceCheck_isOverDue12.days;\n    } else if ((maintenanceCheck === null || maintenanceCheck === void 0 ? void 0 : (_maintenanceCheck_isOverDue5 = maintenanceCheck.isOverDue) === null || _maintenanceCheck_isOverDue5 === void 0 ? void 0 : _maintenanceCheck_isOverDue5.status) === \"Completed\" && lodash_isEmpty__WEBPACK_IMPORTED_MODULE_6___default()(maintenanceCheck === null || maintenanceCheck === void 0 ? void 0 : (_maintenanceCheck_isOverDue6 = maintenanceCheck.isOverDue) === null || _maintenanceCheck_isOverDue6 === void 0 ? void 0 : _maintenanceCheck_isOverDue6.days)) {\n        var _maintenanceCheck_isOverDue13;\n        statusText = maintenanceCheck === null || maintenanceCheck === void 0 ? void 0 : (_maintenanceCheck_isOverDue13 = maintenanceCheck.isOverDue) === null || _maintenanceCheck_isOverDue13 === void 0 ? void 0 : _maintenanceCheck_isOverDue13.status;\n    } else if ((maintenanceCheck === null || maintenanceCheck === void 0 ? void 0 : (_maintenanceCheck_isOverDue7 = maintenanceCheck.isOverDue) === null || _maintenanceCheck_isOverDue7 === void 0 ? void 0 : _maintenanceCheck_isOverDue7.status) === \"Completed\" && !lodash_isEmpty__WEBPACK_IMPORTED_MODULE_6___default()(maintenanceCheck === null || maintenanceCheck === void 0 ? void 0 : (_maintenanceCheck_isOverDue8 = maintenanceCheck.isOverDue) === null || _maintenanceCheck_isOverDue8 === void 0 ? void 0 : _maintenanceCheck_isOverDue8.days) && (maintenanceCheck === null || maintenanceCheck === void 0 ? void 0 : (_maintenanceCheck_isOverDue9 = maintenanceCheck.isOverDue) === null || _maintenanceCheck_isOverDue9 === void 0 ? void 0 : _maintenanceCheck_isOverDue9.days) !== \"Save As Draft\") {\n        var _maintenanceCheck_isOverDue14;\n        statusText = maintenanceCheck === null || maintenanceCheck === void 0 ? void 0 : (_maintenanceCheck_isOverDue14 = maintenanceCheck.isOverDue) === null || _maintenanceCheck_isOverDue14 === void 0 ? void 0 : _maintenanceCheck_isOverDue14.days;\n    }\n    // Only apply styling to overdue items, others are plain text\n    if (isOverdue) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n            className: \"alert w-fit inline-block rounded-md text-nowrap mr-2.5 px-3 py-1\",\n            children: statusText\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n            lineNumber: 169,\n            columnNumber: 13\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n        className: \"text-nowrap\",\n        children: statusText\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n        lineNumber: 175,\n        columnNumber: 12\n    }, undefined);\n};\n_c = StatusBadge;\n// Reusable MaintenanceTable component that accepts props\nfunction MaintenanceTable(param) {\n    let { maintenanceChecks, vessels, crewInfo, showVessel = false } = param;\n    _s();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_7__.usePathname)();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_7__.useSearchParams)();\n    const { vesselIconData, getVesselWithIcon } = (0,_app_lib_vessel_icon_helper__WEBPACK_IMPORTED_MODULE_19__.useVesselIconData)();\n    const columns = (0,_components_filteredTable__WEBPACK_IMPORTED_MODULE_12__.createColumns)([\n        {\n            accessorKey: \"title\",\n            header: (param)=>{\n                let { column } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_13__.DataTableSortHeader, {\n                    column: column,\n                    title: \"Title\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                    lineNumber: 197,\n                    columnNumber: 17\n                }, this);\n            },\n            cell: (param)=>{\n                let { row } = param;\n                var _maintenanceCheck_isOverDue, _maintenanceCheck_basicComponent, _maintenanceCheck_assignedTo;\n                const maintenanceCheck = row.original;\n                const overDueStatus = (_maintenanceCheck_isOverDue = maintenanceCheck.isOverDue) === null || _maintenanceCheck_isOverDue === void 0 ? void 0 : _maintenanceCheck_isOverDue.status;\n                var _maintenanceCheck_name;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                href: \"/maintenance?taskID=\".concat(maintenanceCheck.id, \"&redirect_to=\").concat(pathname, \"?\").concat(searchParams.toString()),\n                                className: \"\".concat(overDueStatus === \"High\" ? \"text-cinnabar-500 hover:text-cinnabar-700\" : overDueStatus === \"Upcoming\" ? \"text-fire-bush-500 hover:text-fire-bush-700\" : \"hover:text-curious-blue-400\"),\n                                children: (_maintenanceCheck_name = maintenanceCheck.name) !== null && _maintenanceCheck_name !== void 0 ? _maintenanceCheck_name : \"Task #\".concat(maintenanceCheck.id, \" (No Name) - \").concat(dayjs__WEBPACK_IMPORTED_MODULE_5___default()(maintenanceCheck.created).format(\"DD/MM/YYYY\"))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                lineNumber: 205,\n                                columnNumber: 29\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                            lineNumber: 204,\n                            columnNumber: 25\n                        }, this),\n                        !showVessel && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:hidden\",\n                            children: ((_maintenanceCheck_basicComponent = maintenanceCheck.basicComponent) === null || _maintenanceCheck_basicComponent === void 0 ? void 0 : _maintenanceCheck_basicComponent.id) > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                href: \"/vessel/info?id=\".concat(maintenanceCheck.basicComponent.id),\n                                className: \"text-sm text-muted-foreground hover:underline\",\n                                children: maintenanceCheck.basicComponent.title\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                lineNumber: 225,\n                                columnNumber: 37\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                            lineNumber: 223,\n                            columnNumber: 29\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"md:hidden space-y-2\",\n                            children: [\n                                ((_maintenanceCheck_assignedTo = maintenanceCheck.assignedTo) === null || _maintenanceCheck_assignedTo === void 0 ? void 0 : _maintenanceCheck_assignedTo.id) > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-muted-foreground\",\n                                            children: [\n                                                \"Assigned to:\",\n                                                \" \"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                            lineNumber: 238,\n                                            columnNumber: 37\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                            href: \"/crew/info?id=\".concat(maintenanceCheck.assignedTo.id),\n                                            className: \"hover:underline\",\n                                            children: maintenanceCheck.assignedTo.name\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                            lineNumber: 241,\n                                            columnNumber: 37\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                    lineNumber: 237,\n                                    columnNumber: 33\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatusBadge, {\n                                        maintenanceCheck: maintenanceCheck\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                        lineNumber: 249,\n                                        columnNumber: 33\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                    lineNumber: 248,\n                                    columnNumber: 29\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                            lineNumber: 235,\n                            columnNumber: 25\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                    lineNumber: 203,\n                    columnNumber: 21\n                }, this);\n            },\n            sortingFn: (rowA, rowB)=>{\n                var _rowA_original, _rowB_original;\n                const valueA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : _rowA_original.name) || \"\";\n                const valueB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : _rowB_original.name) || \"\";\n                return valueA.localeCompare(valueB);\n            }\n        },\n        ...showVessel ? [] : [\n            {\n                accessorKey: \"location\",\n                header: (param)=>{\n                    let { column } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_13__.DataTableSortHeader, {\n                        column: column,\n                        title: \"Location\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                        lineNumber: 272,\n                        columnNumber: 27\n                    }, this);\n                },\n                cellAlignment: \"left\",\n                cell: (param)=>{\n                    let { row } = param;\n                    var _maintenanceCheck_basicComponent, _maintenanceCheck_basicComponent1;\n                    const maintenanceCheck = row.original;\n                    const vesselDetails = getVesselDetails(((_maintenanceCheck_basicComponent = maintenanceCheck.basicComponent) === null || _maintenanceCheck_basicComponent === void 0 ? void 0 : _maintenanceCheck_basicComponent.id) || 0, vessels);\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"hidden lg:block\",\n                        children: ((_maintenanceCheck_basicComponent1 = maintenanceCheck.basicComponent) === null || _maintenanceCheck_basicComponent1 === void 0 ? void 0 : _maintenanceCheck_basicComponent1.id) > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2.5\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_16__.Avatar, {\n                                    size: \"sm\",\n                                    variant: \"secondary\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_16__.AvatarFallback, {\n                                        className: \"text-xs\",\n                                        children: getVesselInitials((vesselDetails === null || vesselDetails === void 0 ? void 0 : vesselDetails.title) || maintenanceCheck.basicComponent.title || undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                        lineNumber: 290,\n                                        columnNumber: 47\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                    lineNumber: 289,\n                                    columnNumber: 43\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    href: \"/vessel/info?id=\".concat(maintenanceCheck.basicComponent.id),\n                                    className: \"hover:underline\",\n                                    children: maintenanceCheck.basicComponent.title\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                    lineNumber: 300,\n                                    columnNumber: 43\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                            lineNumber: 288,\n                            columnNumber: 39\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                        lineNumber: 286,\n                        columnNumber: 31\n                    }, this);\n                },\n                sortingFn: (rowA, rowB)=>{\n                    var _rowA_original_basicComponent, _rowA_original, _rowB_original_basicComponent, _rowB_original;\n                    const valueA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : (_rowA_original_basicComponent = _rowA_original.basicComponent) === null || _rowA_original_basicComponent === void 0 ? void 0 : _rowA_original_basicComponent.title) || \"\";\n                    const valueB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : (_rowB_original_basicComponent = _rowB_original.basicComponent) === null || _rowB_original_basicComponent === void 0 ? void 0 : _rowB_original_basicComponent.title) || \"\";\n                    return valueA.localeCompare(valueB);\n                }\n            }\n        ],\n        {\n            accessorKey: \"assigned\",\n            header: (param)=>{\n                let { column } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_13__.DataTableSortHeader, {\n                    column: column,\n                    title: \"Assigned to\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                    lineNumber: 328,\n                    columnNumber: 17\n                }, this);\n            },\n            cellAlignment: \"left\",\n            breakpoint: \"tablet-sm\",\n            cell: (param)=>{\n                let { row } = param;\n                var _maintenanceCheck_assignedTo, _maintenanceCheck_assignedTo1, _maintenanceCheck_assignedTo2;\n                const maintenanceCheck = row.original;\n                const crewDetails = getCrewDetails(((_maintenanceCheck_assignedTo = maintenanceCheck.assignedTo) === null || _maintenanceCheck_assignedTo === void 0 ? void 0 : _maintenanceCheck_assignedTo.id) || 0, crewInfo);\n                // Temporary debug log to verify the fix\n                if ((_maintenanceCheck_assignedTo1 = maintenanceCheck.assignedTo) === null || _maintenanceCheck_assignedTo1 === void 0 ? void 0 : _maintenanceCheck_assignedTo1.id) {\n                    console.log(\"✅ Crew lookup:\", {\n                        assignedToId: maintenanceCheck.assignedTo.id,\n                        crewFound: !!crewDetails,\n                        crewName: crewDetails ? \"\".concat(crewDetails.firstName, \" \").concat(crewDetails.surname) : \"Not found\"\n                    });\n                }\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"hidden md:block\",\n                    children: ((_maintenanceCheck_assignedTo2 = maintenanceCheck.assignedTo) === null || _maintenanceCheck_assignedTo2 === void 0 ? void 0 : _maintenanceCheck_assignedTo2.id) > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2.5\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_16__.Avatar, {\n                                className: \"h-8 w-8\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_16__.AvatarFallback, {\n                                    className: \"text-xs\",\n                                    children: getCrewInitials(crewDetails === null || crewDetails === void 0 ? void 0 : crewDetails.firstName, crewDetails === null || crewDetails === void 0 ? void 0 : crewDetails.surname)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                    lineNumber: 355,\n                                    columnNumber: 37\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                lineNumber: 354,\n                                columnNumber: 33\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                href: \"/crew/info?id=\".concat(maintenanceCheck.assignedTo.id),\n                                className: \"hover:underline hidden lg:block\",\n                                children: maintenanceCheck.assignedTo.name\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                lineNumber: 362,\n                                columnNumber: 33\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                        lineNumber: 353,\n                        columnNumber: 29\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                    lineNumber: 351,\n                    columnNumber: 21\n                }, this);\n            },\n            sortingFn: (rowA, rowB)=>{\n                var _rowA_original_assignedTo, _rowA_original, _rowB_original_assignedTo, _rowB_original;\n                const valueA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : (_rowA_original_assignedTo = _rowA_original.assignedTo) === null || _rowA_original_assignedTo === void 0 ? void 0 : _rowA_original_assignedTo.name) || \"\";\n                const valueB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : (_rowB_original_assignedTo = _rowB_original.assignedTo) === null || _rowB_original_assignedTo === void 0 ? void 0 : _rowB_original_assignedTo.name) || \"\";\n                return valueA.localeCompare(valueB);\n            }\n        },\n        {\n            accessorKey: \"inventory\",\n            header: \"Inventory item\",\n            cellAlignment: \"left\",\n            breakpoint: \"tablet-lg\",\n            cell: (param)=>{\n                let { row } = param;\n                var _maintenanceCheck_inventory;\n                const maintenanceCheck = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"hidden md:block\",\n                    children: ((_maintenanceCheck_inventory = maintenanceCheck.inventory) === null || _maintenanceCheck_inventory === void 0 ? void 0 : _maintenanceCheck_inventory.id) > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        href: \"/inventory/view?id=\".concat(maintenanceCheck.inventory.id),\n                        className: \"hover:underline\",\n                        children: maintenanceCheck.inventory.item\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                        lineNumber: 391,\n                        columnNumber: 29\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                    lineNumber: 389,\n                    columnNumber: 21\n                }, this);\n            }\n        },\n        {\n            accessorKey: \"status\",\n            header: (param)=>{\n                let { column } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_13__.DataTableSortHeader, {\n                    column: column,\n                    title: \"Status\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                    lineNumber: 404,\n                    columnNumber: 17\n                }, this);\n            },\n            cellAlignment: \"right\",\n            cell: (param)=>{\n                let { row } = param;\n                const maintenanceCheck = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"hidden md:block\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatusBadge, {\n                        maintenanceCheck: maintenanceCheck\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                        lineNumber: 411,\n                        columnNumber: 25\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                    lineNumber: 410,\n                    columnNumber: 21\n                }, this);\n            },\n            sortingFn: (rowA, rowB)=>{\n                var _rowA_original_isOverDue, _rowA_original, _rowB_original_isOverDue, _rowB_original;\n                const valueA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : (_rowA_original_isOverDue = _rowA_original.isOverDue) === null || _rowA_original_isOverDue === void 0 ? void 0 : _rowA_original_isOverDue.days) || \"\";\n                const valueB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : (_rowB_original_isOverDue = _rowB_original.isOverDue) === null || _rowB_original_isOverDue === void 0 ? void 0 : _rowB_original_isOverDue.days) || \"\";\n                return valueA.localeCompare(valueB);\n            }\n        }\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_filteredTable__WEBPACK_IMPORTED_MODULE_12__.DataTable, {\n        columns: columns,\n        data: maintenanceChecks,\n        pageSize: 20,\n        showToolbar: false\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n        lineNumber: 427,\n        columnNumber: 9\n    }, this);\n}\n_s(MaintenanceTable, \"Zp7KzlBBuyYf0TXkH/rj/yT9nuA=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_7__.usePathname,\n        next_navigation__WEBPACK_IMPORTED_MODULE_7__.useSearchParams,\n        _app_lib_vessel_icon_helper__WEBPACK_IMPORTED_MODULE_19__.useVesselIconData\n    ];\n});\n_c1 = MaintenanceTable;\nfunction TaskList() {\n    _s1();\n    var _s = $RefreshSig$();\n    const [maintenanceChecks, setMaintenanceChecks] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    const [filteredMaintenanceChecks, setFilteredMaintenanceChecks] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    const [vessels, setVessels] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    const [crewInfo, setCrewInfo] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    const [filter, setFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [keywordFilter, setKeywordFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [permissions, setPermissions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [edit_task, setEdit_task] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_7__.usePathname)();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_7__.useSearchParams)();\n    const { getVesselWithIcon } = (0,_app_lib_vessel_icon_helper__WEBPACK_IMPORTED_MODULE_19__.useVesselIconData)();\n    const bp = (0,_components_hooks_useBreakpoints__WEBPACK_IMPORTED_MODULE_20__.useBreakpoints)();\n    const init_permissions = ()=>{\n        if (permissions) {\n            if ((0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_8__.hasPermission)(\"EDIT_TASK\", permissions)) {\n                setEdit_task(true);\n            } else {\n                setEdit_task(false);\n            }\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setPermissions(_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_8__.getPermissions);\n        init_permissions();\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (permissions) {\n            init_permissions();\n        }\n    }, [\n        permissions\n    ]);\n    const [queryMaintenanceChecks] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_26__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_2__.GET_MAINTENANCE_CHECK_LIST, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            const data = response.readComponentMaintenanceCheckList[0].list;\n            if (data) {\n                handleSetMaintenanceChecks(data);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"queryMaintenanceChecks error\", error);\n        }\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isLoading) {\n            loadMaintenanceChecks();\n            setIsLoading(false);\n        }\n    }, [\n        isLoading\n    ]);\n    const loadMaintenanceChecks = async ()=>{\n        await queryMaintenanceChecks({\n            variables: {\n                inventoryID: 0,\n                vesselID: 0\n            }\n        });\n    };\n    const handleSetVessels = (vessels)=>{\n        const activeVessels = vessels.filter((vessel)=>!vessel.archived);\n        const appendedData = activeVessels.map((item)=>({\n                ...item\n            }));\n        appendedData.push({\n            title: \"Other\",\n            id: 0\n        });\n        setVessels(appendedData);\n    };\n    const getVesselList = function(handleSetVessels) {\n        let offline = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : false;\n        _s();\n        const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n        const vesselModel = new _app_offline_models_vessel__WEBPACK_IMPORTED_MODULE_24__[\"default\"]();\n        (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n            if (isLoading) {\n                loadVessels();\n                setIsLoading(false);\n            }\n        }, [\n            isLoading\n        ]);\n        const [queryVessels] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_26__.useLazyQuery)(_queries__WEBPACK_IMPORTED_MODULE_23__.ReadVessels, {\n            fetchPolicy: \"cache-and-network\",\n            onCompleted: (queryVesselResponse)=>{\n                if (queryVesselResponse.readVessels.nodes) {\n                    handleSetVessels(queryVesselResponse.readVessels.nodes);\n                }\n            },\n            onError: (error)=>{\n                console.error(\"queryVessels error\", error);\n            }\n        });\n        const loadVessels = async ()=>{\n            if (offline) {\n                const response = await vesselModel.getAll();\n                handleSetVessels(response);\n            } else {\n                await queryVessels({\n                    variables: {\n                        limit: 200,\n                        offset: 0\n                    }\n                });\n            }\n        };\n    };\n    _s(getVesselList, \"pQOK42e7v9ItR64U9CP/qx2cQME=\", false, function() {\n        return [\n            _apollo_client__WEBPACK_IMPORTED_MODULE_26__.useLazyQuery\n        ];\n    });\n    getVesselList(handleSetVessels);\n    const handleSetMaintenanceChecks = (tasks)=>{\n        setMaintenanceChecks(tasks);\n        setFilteredMaintenanceChecks(tasks);\n        const appendedData = Array.from(new Set(tasks.filter((item)=>item.assignedTo.id > 0).map((item)=>item.assignedTo.id)));\n        loadCrewMemberInfo(appendedData);\n    };\n    const [queryCrewMemberInfo] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_26__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_2__.GET_CREW_BY_IDS, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            const data = response.readSeaLogsMembers.nodes;\n            if (data) {\n                setCrewInfo(data);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"queryCrewMemberInfo error\", error);\n        }\n    });\n    const loadCrewMemberInfo = async (crewId)=>{\n        await queryCrewMemberInfo({\n            variables: {\n                crewMemberIDs: crewId.length > 0 ? crewId : [\n                    0\n                ]\n            }\n        });\n    };\n    const handleFilterOnChange = (param)=>{\n        let { type, data } = param;\n        const searchFilter = {\n            ...filter\n        };\n        let filteredTasks = maintenanceChecks || [];\n        // Vessel filter\n        if (type === \"vessel\") {\n            if (Array.isArray(data) && data.length > 0) {\n                searchFilter.basicComponentID = {\n                    in: data.map((item)=>+item.value)\n                };\n            } else if (data && !Array.isArray(data)) {\n                searchFilter.basicComponentID = {\n                    eq: +data.value\n                };\n            } else {\n                delete searchFilter.basicComponentID;\n            }\n        }\n        // Status filter\n        if (type === \"status\") {\n            if (Array.isArray(data) && data.length > 0) {\n                searchFilter.status = {\n                    in: data.map((item)=>item.value)\n                };\n            } else if (data && !Array.isArray(data)) {\n                searchFilter.status = {\n                    eq: data.value\n                };\n            } else {\n                delete searchFilter.status;\n            }\n        }\n        // Assigned member filter\n        if (type === \"member\") {\n            if (Array.isArray(data) && data.length > 0) {\n                searchFilter.assignedToID = {\n                    in: data.map((item)=>+item.value)\n                };\n            } else if (data && !Array.isArray(data)) {\n                searchFilter.assignedToID = {\n                    eq: +data.value\n                };\n            } else {\n                delete searchFilter.assignedToID;\n            }\n        }\n        // Date range\n        if (type === \"dateRange\") {\n            if ((data === null || data === void 0 ? void 0 : data.startDate) && (data === null || data === void 0 ? void 0 : data.endDate)) {\n                searchFilter.expires = {\n                    gte: data.startDate,\n                    lte: data.endDate\n                };\n            } else {\n                delete searchFilter.expires;\n            }\n        }\n        // Category\n        if (type === \"category\") {\n            if (Array.isArray(data) && data.length > 0) {\n                searchFilter.maintenanceCategoryID = {\n                    in: data.map((item)=>+item.value)\n                };\n            } else if (data && !Array.isArray(data)) {\n                searchFilter.maintenanceCategoryID = {\n                    eq: +data.value\n                };\n            } else {\n                delete searchFilter.maintenanceCategoryID;\n            }\n        }\n        // Recurring filter - handle client-side filtering\n        let recurringFilter = null;\n        if (type === \"recurring\") {\n            if (data && !Array.isArray(data)) {\n                recurringFilter = data.value;\n            }\n        }\n        // Keyword filter\n        let keyFilter = keywordFilter;\n        if (type === \"keyword\" || keyFilter && keyFilter.length > 0) {\n            var _data_value;\n            const keyword = data === null || data === void 0 ? void 0 : (_data_value = data.value) === null || _data_value === void 0 ? void 0 : _data_value.trim().toLowerCase();\n            if (keyword && keyword.length > 0) {\n                filteredTasks = filteredTasks.filter((maintenanceCheck)=>[\n                        maintenanceCheck.name,\n                        maintenanceCheck.comments,\n                        maintenanceCheck.workOrderNumber\n                    ].some((field)=>field === null || field === void 0 ? void 0 : field.toLowerCase().includes(keyword)));\n                keyFilter = data.value;\n            } else {\n                keyFilter = null;\n            }\n        }\n        // Filtering based on current searchFilter\n        // Filter by vessel (basicComponentID)\n        if (searchFilter.basicComponentID) {\n            const ids = searchFilter.basicComponentID.in || [\n                searchFilter.basicComponentID.eq\n            ];\n            filteredTasks = filteredTasks.filter((mc)=>{\n                var _mc_basicComponent;\n                return ids.includes((_mc_basicComponent = mc.basicComponent) === null || _mc_basicComponent === void 0 ? void 0 : _mc_basicComponent.id);\n            });\n        }\n        // Filter by status\n        if (searchFilter.status) {\n            const statuses = searchFilter.status.in || [\n                searchFilter.status.eq\n            ];\n            filteredTasks = filteredTasks.filter((mc)=>statuses.includes(mc.status));\n        }\n        // Filter by assignedToID\n        if (searchFilter.assignedToID) {\n            const ids = searchFilter.assignedToID.in || [\n                searchFilter.assignedToID.eq\n            ];\n            filteredTasks = filteredTasks.filter((mc)=>{\n                var _mc_assignedTo;\n                return ids.includes((_mc_assignedTo = mc.assignedTo) === null || _mc_assignedTo === void 0 ? void 0 : _mc_assignedTo.id);\n            });\n        }\n        // Filter by category\n        if (searchFilter.maintenanceCategoryID) {\n            const ids = searchFilter.maintenanceCategoryID.in || [\n                searchFilter.maintenanceCategoryID.eq\n            ];\n            filteredTasks = filteredTasks.filter((mc)=>ids.includes(mc.maintenanceCategoryID));\n        }\n        // Filter by date range\n        if (searchFilter.expires && searchFilter.expires.gte && searchFilter.expires.lte) {\n            filteredTasks = filteredTasks.filter((mc)=>dayjs__WEBPACK_IMPORTED_MODULE_5___default()(mc.startDate).isAfter(dayjs__WEBPACK_IMPORTED_MODULE_5___default()(searchFilter.expires.gte)) && dayjs__WEBPACK_IMPORTED_MODULE_5___default()(mc.startDate).isBefore(dayjs__WEBPACK_IMPORTED_MODULE_5___default()(searchFilter.expires.lte)));\n        }\n        // Filter by recurring status\n        if (recurringFilter) {\n            if (recurringFilter === \"recurring\") {\n                // Recurring tasks have recurringID > 0\n                filteredTasks = filteredTasks.filter((mc)=>mc.recurringID > 0);\n            } else if (recurringFilter === \"one-off\") {\n                // One-off tasks have recurringID = 0 or null\n                filteredTasks = filteredTasks.filter((mc)=>!mc.recurringID || mc.recurringID === 0);\n            }\n        }\n        // Set updated filters\n        setFilter(searchFilter);\n        setKeywordFilter(keyFilter);\n        setFilteredMaintenanceChecks(filteredTasks);\n    // Optionally call API: loadMaintenanceChecks(searchFilter, keyFilter);\n    };\n    const downloadCsv = ()=>{\n        if (!maintenanceChecks || !vessels) {\n            return;\n        }\n        const csvEntries = [\n            [\n                \"task\",\n                \"location\",\n                \"assigned to\",\n                \"due\"\n            ]\n        ];\n        maintenanceChecks.filter((maintenanceCheck)=>maintenanceCheck.status !== \"Save_As_Draft\").forEach((maintenanceCheck)=>{\n            var _maintenanceCheck_assignedTo, _maintenanceCheck_assignedTo1;\n            const crewDetails = getCrewDetails(((_maintenanceCheck_assignedTo = maintenanceCheck.assignedTo) === null || _maintenanceCheck_assignedTo === void 0 ? void 0 : _maintenanceCheck_assignedTo.id) || 0, crewInfo);\n            const assignedToName = crewDetails ? \"\".concat(crewDetails.firstName, \" \").concat(crewDetails.surname) : ((_maintenanceCheck_assignedTo1 = maintenanceCheck.assignedTo) === null || _maintenanceCheck_assignedTo1 === void 0 ? void 0 : _maintenanceCheck_assignedTo1.name) || \"\";\n            csvEntries.push([\n                maintenanceCheck.name,\n                (vessels === null || vessels === void 0 ? void 0 : vessels.filter((vessel)=>{\n                    var _maintenanceCheck_basicComponent;\n                    return (vessel === null || vessel === void 0 ? void 0 : vessel.id) == ((_maintenanceCheck_basicComponent = maintenanceCheck.basicComponent) === null || _maintenanceCheck_basicComponent === void 0 ? void 0 : _maintenanceCheck_basicComponent.id);\n                }).map((vessel)=>vessel.title).join(\", \")) || \"\",\n                assignedToName,\n                (0,_reporting_maintenance_status_activity_report__WEBPACK_IMPORTED_MODULE_9__.dueStatusLabel)(maintenanceCheck.isOverDue)\n            ]);\n        });\n        (0,_app_helpers_csvHelper__WEBPACK_IMPORTED_MODULE_10__.exportCsv)(csvEntries);\n    };\n    const downloadPdf = ()=>{\n        if (!maintenanceChecks || !vessels) {\n            return;\n        }\n        const headers = [\n            [\n                \"Task Name\",\n                \"Location\",\n                \"Assigned To\",\n                \"Due\"\n            ]\n        ];\n        const body = maintenanceChecks.filter((maintenanceCheck)=>maintenanceCheck.status !== \"Save_As_Draft\").map((maintenanceCheck)=>{\n            var _maintenanceCheck_assignedTo, _maintenanceCheck_assignedTo1;\n            const crewDetails = getCrewDetails(((_maintenanceCheck_assignedTo = maintenanceCheck.assignedTo) === null || _maintenanceCheck_assignedTo === void 0 ? void 0 : _maintenanceCheck_assignedTo.id) || 0, crewInfo);\n            const assignedToName = crewDetails ? \"\".concat(crewDetails.firstName, \" \").concat(crewDetails.surname) : ((_maintenanceCheck_assignedTo1 = maintenanceCheck.assignedTo) === null || _maintenanceCheck_assignedTo1 === void 0 ? void 0 : _maintenanceCheck_assignedTo1.name) || \"\";\n            return [\n                maintenanceCheck.name,\n                (vessels === null || vessels === void 0 ? void 0 : vessels.filter((vessel)=>{\n                    var _maintenanceCheck_basicComponent;\n                    return (vessel === null || vessel === void 0 ? void 0 : vessel.id) == ((_maintenanceCheck_basicComponent = maintenanceCheck.basicComponent) === null || _maintenanceCheck_basicComponent === void 0 ? void 0 : _maintenanceCheck_basicComponent.id);\n                }).map((vessel)=>vessel.title).join(\", \")) || \"\",\n                assignedToName,\n                (0,_reporting_maintenance_status_activity_report__WEBPACK_IMPORTED_MODULE_9__.dueStatusLabel)(maintenanceCheck.isOverDue)\n            ];\n        });\n        (0,_app_helpers_pdfHelper__WEBPACK_IMPORTED_MODULE_11__.exportPdfTable)({\n            headers,\n            body\n        });\n    };\n    const createMaintenanceColumns = (crewInfo, getVesselWithIcon)=>(0,_components_filteredTable__WEBPACK_IMPORTED_MODULE_12__.createColumns)([\n            {\n                accessorKey: \"title\",\n                header: (param)=>{\n                    let { column } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_13__.DataTableSortHeader, {\n                        column: column,\n                        title: \"Title\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                        lineNumber: 844,\n                        columnNumber: 21\n                    }, this);\n                },\n                cell: (param)=>{\n                    let { row } = param;\n                    var _maintenanceCheck_assignedTo, _maintenanceCheck_isOverDue, _maintenanceCheck_isOverDue1, _maintenanceCheck_inventory, _maintenanceCheck_assignedTo1, _maintenanceCheck_basicComponent;\n                    const maintenanceCheck = row.original;\n                    const crewDetails = getCrewDetails(((_maintenanceCheck_assignedTo = maintenanceCheck.assignedTo) === null || _maintenanceCheck_assignedTo === void 0 ? void 0 : _maintenanceCheck_assignedTo.id) || 0, crewInfo);\n                    const overDueStatus = (_maintenanceCheck_isOverDue = maintenanceCheck.isOverDue) === null || _maintenanceCheck_isOverDue === void 0 ? void 0 : _maintenanceCheck_isOverDue.status;\n                    const overDueDays = (_maintenanceCheck_isOverDue1 = maintenanceCheck.isOverDue) === null || _maintenanceCheck_isOverDue1 === void 0 ? void 0 : _maintenanceCheck_isOverDue1.day;\n                    var _maintenanceCheck_name, _maintenanceCheck_name1;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_21__.Card, {\n                                className: \"xs:hidden space-y-3 p-2.5 min-h-20 w-full shadow-none rounded-none bg-transparent\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_21__.CardContent, {\n                                    className: \"p-0 space-y-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                            href: \"/maintenance?taskID=\".concat(maintenanceCheck.id, \"&redirect_to=\").concat(pathname, \"?\").concat(searchParams.toString()),\n                                            className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_25__.cn)(\"text-base\", overDueStatus === \"High\" ? \"text-cinnabar-500 hover:text-cinnabar-700\" : overDueStatus === \"Upcoming\" ? \"text-fire-bush-500 hover:text-fire-bush-700\" : \"hover:text-curious-blue-400\"),\n                                            children: [\n                                                (_maintenanceCheck_name = maintenanceCheck.name) !== null && _maintenanceCheck_name !== void 0 ? _maintenanceCheck_name : \"Task #\".concat(maintenanceCheck.id, \" (No Name) - \").concat(dayjs__WEBPACK_IMPORTED_MODULE_5___default()(maintenanceCheck.created).format(\"DD/MM/YYYY\")),\n                                                \" \"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                            lineNumber: 862,\n                                            columnNumber: 37\n                                        }, this),\n                                        ((_maintenanceCheck_inventory = maintenanceCheck.inventory) === null || _maintenanceCheck_inventory === void 0 ? void 0 : _maintenanceCheck_inventory.id) > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-col\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xs text-muted-foreground\",\n                                                    children: \"Inventory item\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                                    lineNumber: 881,\n                                                    columnNumber: 45\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                    href: \"/inventory/view?id=\".concat(maintenanceCheck.inventory.id),\n                                                    className: \"hover:underline text-sm\",\n                                                    children: maintenanceCheck.inventory.item\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                                    lineNumber: 884,\n                                                    columnNumber: 45\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                            lineNumber: 880,\n                                            columnNumber: 41\n                                        }, this),\n                                        ((_maintenanceCheck_assignedTo1 = maintenanceCheck.assignedTo) === null || _maintenanceCheck_assignedTo1 === void 0 ? void 0 : _maintenanceCheck_assignedTo1.id) > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_16__.Avatar, {\n                                            variant: \"secondary\",\n                                            className: \"h-8 w-8\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_16__.AvatarFallback, {\n                                                className: \"text-xs\",\n                                                children: getCrewInitials(crewDetails === null || crewDetails === void 0 ? void 0 : crewDetails.firstName, crewDetails === null || crewDetails === void 0 ? void 0 : crewDetails.surname)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                                lineNumber: 900,\n                                                columnNumber: 45\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                            lineNumber: 897,\n                                            columnNumber: 41\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                    lineNumber: 860,\n                                    columnNumber: 33\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                lineNumber: 859,\n                                columnNumber: 29\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"hidden xs:block\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center flex-nowrap gap-2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                            href: \"/maintenance?taskID=\".concat(maintenanceCheck.id, \"&redirect_to=\").concat(pathname, \"?\").concat(searchParams.toString()),\n                                            className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_25__.cn)(\"text-base\", overDueStatus === \"High\" ? \"text-cinnabar-500 hover:text-cinnabar-700\" : overDueStatus === \"Upcoming\" ? \"text-fire-bush-500 hover:text-fire-bush-700\" : \"hover:text-curious-blue-400\"),\n                                            children: (_maintenanceCheck_name1 = maintenanceCheck.name) !== null && _maintenanceCheck_name1 !== void 0 ? _maintenanceCheck_name1 : \"Task #\".concat(maintenanceCheck.id, \" (No Name) - \").concat(dayjs__WEBPACK_IMPORTED_MODULE_5___default()(maintenanceCheck.created).format(\"DD/MM/YYYY\"))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                            lineNumber: 914,\n                                            columnNumber: 37\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                        lineNumber: 913,\n                                        columnNumber: 33\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"md:hidden\",\n                                        children: ((_maintenanceCheck_basicComponent = maintenanceCheck.basicComponent) === null || _maintenanceCheck_basicComponent === void 0 ? void 0 : _maintenanceCheck_basicComponent.id) > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                            href: \"/vessel/info?id=\".concat(maintenanceCheck.basicComponent.id),\n                                            className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_25__.cn)(\"text-base\", overDueStatus === \"High\" ? \"text-cinnabar-500 hover:text-cinnabar-700\" : overDueStatus === \"Upcoming\" ? \"text-fire-bush-500 hover:text-fire-bush-700\" : \"hover:text-curious-blue-400\"),\n                                            children: maintenanceCheck.basicComponent.title\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                            lineNumber: 934,\n                                            columnNumber: 41\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                        lineNumber: 931,\n                                        columnNumber: 33\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                lineNumber: 912,\n                                columnNumber: 29\n                            }, this)\n                        ]\n                    }, void 0, true);\n                },\n                sortingFn: (rowA, rowB)=>{\n                    var _rowA_original, _rowB_original;\n                    const valueA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : _rowA_original.name) || \"\";\n                    const valueB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : _rowB_original.name) || \"\";\n                    return valueA.localeCompare(valueB);\n                }\n            },\n            {\n                accessorKey: \"location\",\n                header: (param)=>{\n                    let { column } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_13__.DataTableSortHeader, {\n                        column: column,\n                        title: \"Location\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                        lineNumber: 968,\n                        columnNumber: 21\n                    }, this);\n                },\n                cellAlignment: \"left\",\n                breakpoint: \"laptop\",\n                cell: (param)=>{\n                    let { row } = param;\n                    var _maintenanceCheck_basicComponent, _maintenanceCheck_basicComponent1, _maintenanceCheck_basicComponent2;\n                    const maintenanceCheck = row.original;\n                    const vesselDetails = getVesselDetails(((_maintenanceCheck_basicComponent = maintenanceCheck.basicComponent) === null || _maintenanceCheck_basicComponent === void 0 ? void 0 : _maintenanceCheck_basicComponent.id) || 0, vessels);\n                    // Get vessel with icon data\n                    const vesselWithIcon = getVesselWithIcon(((_maintenanceCheck_basicComponent1 = maintenanceCheck.basicComponent) === null || _maintenanceCheck_basicComponent1 === void 0 ? void 0 : _maintenanceCheck_basicComponent1.id) || 0, vesselDetails);\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: ((_maintenanceCheck_basicComponent2 = maintenanceCheck.basicComponent) === null || _maintenanceCheck_basicComponent2 === void 0 ? void 0 : _maintenanceCheck_basicComponent2.id) > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2.5\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_17__.Tooltip, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_17__.TooltipTrigger, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"min-w-fit\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_vessels_vesel_icon__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                    vessel: vesselWithIcon\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                                    lineNumber: 992,\n                                                    columnNumber: 49\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                                lineNumber: 991,\n                                                columnNumber: 45\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                            lineNumber: 990,\n                                            columnNumber: 41\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_17__.TooltipContent, {\n                                            children: maintenanceCheck.basicComponent.title\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                            lineNumber: 997,\n                                            columnNumber: 41\n                                        }, this)\n                                    ]\n                                }, vesselDetails === null || vesselDetails === void 0 ? void 0 : vesselDetails.id, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                    lineNumber: 989,\n                                    columnNumber: 37\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    href: \"/vessel/info?id=\".concat(maintenanceCheck.basicComponent.id),\n                                    className: \"hover:underline\",\n                                    children: maintenanceCheck.basicComponent.title\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                    lineNumber: 1004,\n                                    columnNumber: 37\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                            lineNumber: 988,\n                            columnNumber: 33\n                        }, this)\n                    }, void 0, false);\n                },\n                sortingFn: (rowA, rowB)=>{\n                    var _rowA_original_basicComponent, _rowA_original, _rowB_original_basicComponent, _rowB_original;\n                    const valueA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : (_rowA_original_basicComponent = _rowA_original.basicComponent) === null || _rowA_original_basicComponent === void 0 ? void 0 : _rowA_original_basicComponent.title) || \"\";\n                    const valueB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : (_rowB_original_basicComponent = _rowB_original.basicComponent) === null || _rowB_original_basicComponent === void 0 ? void 0 : _rowB_original_basicComponent.title) || \"\";\n                    return valueA.localeCompare(valueB);\n                }\n            },\n            {\n                accessorKey: \"assigned\",\n                header: (param)=>{\n                    let { column } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_13__.DataTableSortHeader, {\n                        column: column,\n                        title: \"Assigned to\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                        lineNumber: 1026,\n                        columnNumber: 21\n                    }, this);\n                },\n                cellAlignment: \"left\",\n                breakpoint: \"tablet-md\",\n                cell: (param)=>{\n                    let { row } = param;\n                    var _maintenanceCheck_assignedTo, _maintenanceCheck_assignedTo1;\n                    const maintenanceCheck = row.original;\n                    const crewDetails = getCrewDetails(((_maintenanceCheck_assignedTo = maintenanceCheck.assignedTo) === null || _maintenanceCheck_assignedTo === void 0 ? void 0 : _maintenanceCheck_assignedTo.id) || 0, crewInfo);\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: ((_maintenanceCheck_assignedTo1 = maintenanceCheck.assignedTo) === null || _maintenanceCheck_assignedTo1 === void 0 ? void 0 : _maintenanceCheck_assignedTo1.id) > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2.5\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_16__.Avatar, {\n                                    variant: \"secondary\",\n                                    className: \"h-8 w-8\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_16__.AvatarFallback, {\n                                        className: \"text-xs\",\n                                        children: getCrewInitials(crewDetails === null || crewDetails === void 0 ? void 0 : crewDetails.firstName, crewDetails === null || crewDetails === void 0 ? void 0 : crewDetails.surname)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                        lineNumber: 1044,\n                                        columnNumber: 41\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                    lineNumber: 1041,\n                                    columnNumber: 37\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    href: \"/crew/info?id=\".concat(maintenanceCheck.assignedTo.id),\n                                    className: \"hover:underline hidden tablet-md:block\",\n                                    children: maintenanceCheck.assignedTo.name\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                    lineNumber: 1051,\n                                    columnNumber: 37\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                            lineNumber: 1040,\n                            columnNumber: 33\n                        }, this)\n                    }, void 0, false);\n                },\n                sortingFn: (rowA, rowB)=>{\n                    var _rowA_original_assignedTo, _rowA_original, _rowB_original_assignedTo, _rowB_original;\n                    const valueA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : (_rowA_original_assignedTo = _rowA_original.assignedTo) === null || _rowA_original_assignedTo === void 0 ? void 0 : _rowA_original_assignedTo.name) || \"\";\n                    const valueB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : (_rowB_original_assignedTo = _rowB_original.assignedTo) === null || _rowB_original_assignedTo === void 0 ? void 0 : _rowB_original_assignedTo.name) || \"\";\n                    return valueA.localeCompare(valueB);\n                }\n            },\n            {\n                accessorKey: \"inventory\",\n                header: \"Inventory item\",\n                cellAlignment: \"left\",\n                breakpoint: \"phablet\",\n                cell: (param)=>{\n                    let { row } = param;\n                    var _maintenanceCheck_inventory;\n                    const maintenanceCheck = row.original;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: ((_maintenanceCheck_inventory = maintenanceCheck.inventory) === null || _maintenanceCheck_inventory === void 0 ? void 0 : _maintenanceCheck_inventory.id) > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            href: \"/inventory/view?id=\".concat(maintenanceCheck.inventory.id),\n                            className: \"hover:underline\",\n                            children: maintenanceCheck.inventory.item\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                            lineNumber: 1080,\n                            columnNumber: 33\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: \"-\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                            lineNumber: 1086,\n                            columnNumber: 33\n                        }, this)\n                    }, void 0, false);\n                }\n            },\n            {\n                accessorKey: \"status\",\n                header: (param)=>{\n                    let { column } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_13__.DataTableSortHeader, {\n                        column: column,\n                        title: \"Status\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                        lineNumber: 1095,\n                        columnNumber: 21\n                    }, this);\n                },\n                cellAlignment: \"right\",\n                cell: (param)=>{\n                    let { row } = param;\n                    var _maintenanceCheck_isOverDue, _maintenanceCheck_isOverDue1;\n                    const maintenanceCheck = row.original;\n                    if (!maintenanceCheck) {\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: \"-\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                            lineNumber: 1102,\n                            columnNumber: 32\n                        }, this);\n                    }\n                    const overDueStatus = (_maintenanceCheck_isOverDue = maintenanceCheck.isOverDue) === null || _maintenanceCheck_isOverDue === void 0 ? void 0 : _maintenanceCheck_isOverDue.status;\n                    const overDueDays = (_maintenanceCheck_isOverDue1 = maintenanceCheck.isOverDue) === null || _maintenanceCheck_isOverDue1 === void 0 ? void 0 : _maintenanceCheck_isOverDue1.day;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: overDueStatus === \"High\" ? !bp[\"tablet-lg\"] ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \" items-end mr-2.5 text-nowrap w-fit\\n                                                    \".concat(overDueStatus === \"High\" ? \"alert text-sm !px-1.5 !py-0.5 xs:px-3 xs:py-1 whitespace-nowrap\" : \"\", \"\\n                                                    \"),\n                            children: overDueDays * -1 + \" days ago\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                            lineNumber: 1112,\n                            columnNumber: 37\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatusBadge, {\n                            maintenanceCheck: maintenanceCheck\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                            lineNumber: 1119,\n                            columnNumber: 37\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatusBadge, {\n                            maintenanceCheck: maintenanceCheck\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                            lineNumber: 1124,\n                            columnNumber: 33\n                        }, this)\n                    }, void 0, false);\n                },\n                sortingFn: (rowA, rowB)=>{\n                    var _rowA_original_isOverDue, _rowA_original, _rowB_original_isOverDue, _rowB_original;\n                    const valueA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : (_rowA_original_isOverDue = _rowA_original.isOverDue) === null || _rowA_original_isOverDue === void 0 ? void 0 : _rowA_original_isOverDue.days) || \"\";\n                    const valueB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : (_rowB_original_isOverDue = _rowB_original.isOverDue) === null || _rowB_original_isOverDue === void 0 ? void 0 : _rowB_original_isOverDue.days) || \"\";\n                    return valueA.localeCompare(valueB);\n                }\n            }\n        ]);\n    const columns = createMaintenanceColumns(crewInfo || [], getVesselWithIcon);\n    // Row status evaluator for maintenance tasks\n    const getMaintenanceRowStatus = (maintenanceCheck)=>{\n        var _maintenanceCheck_isOverDue;\n        // Skip completed, archived, or draft tasks\n        if (maintenanceCheck.status === \"Completed\" || maintenanceCheck.archived || maintenanceCheck.status === \"Save_As_Draft\") {\n            return \"normal\";\n        }\n        const overDueStatus = (_maintenanceCheck_isOverDue = maintenanceCheck.isOverDue) === null || _maintenanceCheck_isOverDue === void 0 ? void 0 : _maintenanceCheck_isOverDue.status;\n        // Use the pre-calculated status values from the system\n        switch(overDueStatus){\n            case \"High\":\n                return \"overdue\" // Red highlighting\n                ;\n            case \"Upcoming\":\n                return \"upcoming\" // Orange highlighting\n                ;\n            case \"Medium\":\n            case \"Open\":\n            default:\n                return \"normal\" // No highlighting\n                ;\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_list_header__WEBPACK_IMPORTED_MODULE_15__.ListHeader, {\n                title: \"Maintenance\",\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_lib_icons__WEBPACK_IMPORTED_MODULE_22__.SealogsMaintenanceIcon, {\n                    className: \"h-12 w-12 ring-1 p-1 rounded-full bg-[#fff]\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                    lineNumber: 1177,\n                    columnNumber: 21\n                }, void 0),\n                actions: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_filter_components_maintenance_actions__WEBPACK_IMPORTED_MODULE_14__.MaintenanceFilterActions, {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                    lineNumber: 1179,\n                    columnNumber: 26\n                }, void 0),\n                titleClassName: \"\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                lineNumber: 1174,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-16\",\n                children: maintenanceChecks && vessels ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_filteredTable__WEBPACK_IMPORTED_MODULE_12__.DataTable, {\n                    columns: columns,\n                    data: filteredMaintenanceChecks || [],\n                    pageSize: 20,\n                    onChange: handleFilterOnChange,\n                    rowStatus: getMaintenanceRowStatus\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                    lineNumber: 1184,\n                    columnNumber: 21\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_skeletons__WEBPACK_IMPORTED_MODULE_3__.TableSkeleton, {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                    lineNumber: 1192,\n                    columnNumber: 21\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                lineNumber: 1182,\n                columnNumber: 13\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s1(TaskList, \"LFHio0/i4l3OQsePNWySacFXTG4=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_7__.usePathname,\n        next_navigation__WEBPACK_IMPORTED_MODULE_7__.useSearchParams,\n        _app_lib_vessel_icon_helper__WEBPACK_IMPORTED_MODULE_19__.useVesselIconData,\n        _components_hooks_useBreakpoints__WEBPACK_IMPORTED_MODULE_20__.useBreakpoints,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_26__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_26__.useLazyQuery\n    ];\n});\n_c2 = TaskList;\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"StatusBadge\");\n$RefreshReg$(_c1, \"MaintenanceTable\");\n$RefreshReg$(_c2, \"TaskList\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/maintenance/list/list.tsx\n"));

/***/ })

});