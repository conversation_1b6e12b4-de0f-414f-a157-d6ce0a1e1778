"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/app/ui/maintenance/list/list.tsx":
/*!**********************************************!*\
  !*** ./src/app/ui/maintenance/list/list.tsx ***!
  \**********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MaintenanceTable: function() { return /* binding */ MaintenanceTable; },\n/* harmony export */   StatusBadge: function() { return /* binding */ StatusBadge; },\n/* harmony export */   createMaintenanceTableColumns: function() { return /* binding */ createMaintenanceTableColumns; },\n/* harmony export */   \"default\": function() { return /* binding */ TaskList; },\n/* harmony export */   getVesselDetails: function() { return /* binding */ getVesselDetails; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/react/hooks/useLazyQuery.js\");\n/* harmony import */ var _app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/app/lib/graphQL/query */ \"(app-pages-browser)/./src/app/lib/graphQL/query/index.ts\");\n/* harmony import */ var _components_skeletons__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/skeletons */ \"(app-pages-browser)/./src/components/skeletons.tsx\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/api/link.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! dayjs */ \"(app-pages-browser)/./node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/dayjs.min.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(dayjs__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var lodash_isEmpty__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! lodash/isEmpty */ \"(app-pages-browser)/./node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/isEmpty.js\");\n/* harmony import */ var lodash_isEmpty__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(lodash_isEmpty__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_filteredTable__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/filteredTable */ \"(app-pages-browser)/./src/components/filteredTable.tsx\");\n/* harmony import */ var _components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/data-table-sort-header */ \"(app-pages-browser)/./src/components/data-table-sort-header.tsx\");\n/* harmony import */ var _components_filter_components_maintenance_actions__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/filter/components/maintenance-actions */ \"(app-pages-browser)/./src/components/filter/components/maintenance-actions.tsx\");\n/* harmony import */ var _components_ui_list_header__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/ui/list-header */ \"(app-pages-browser)/./src/components/ui/list-header.tsx\");\n/* harmony import */ var _components_ui_avatar__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/ui/avatar */ \"(app-pages-browser)/./src/components/ui/avatar.tsx\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.ts\");\n/* harmony import */ var _vessels_vesel_icon__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ../../vessels/vesel-icon */ \"(app-pages-browser)/./src/app/ui/vessels/vesel-icon.tsx\");\n/* harmony import */ var _app_lib_vessel_icon_helper__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/app/lib/vessel-icon-helper */ \"(app-pages-browser)/./src/app/lib/vessel-icon-helper.tsx\");\n/* harmony import */ var _components_hooks_useBreakpoints__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/components/hooks/useBreakpoints */ \"(app-pages-browser)/./src/components/hooks/useBreakpoints.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _app_lib_icons__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @/app/lib/icons */ \"(app-pages-browser)/./src/app/lib/icons/index.ts\");\n/* harmony import */ var _queries__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! ./queries */ \"(app-pages-browser)/./src/app/ui/maintenance/list/queries.ts\");\n/* harmony import */ var _app_offline_models_vessel__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! @/app/offline/models/vessel */ \"(app-pages-browser)/./src/app/offline/models/vessel.js\");\n/* harmony import */ var _app_lib_utils__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! @/app/lib/utils */ \"(app-pages-browser)/./src/app/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ getVesselDetails,createMaintenanceTableColumns,StatusBadge,MaintenanceTable,default auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// Helper functions\nconst getCrewInitials = (firstName, surname)=>{\n    var _firstName_charAt, _surname_charAt;\n    if (!firstName && !surname) return \"??\";\n    const first = (firstName === null || firstName === void 0 ? void 0 : (_firstName_charAt = firstName.charAt(0)) === null || _firstName_charAt === void 0 ? void 0 : _firstName_charAt.toUpperCase()) || \"\";\n    const last = (surname === null || surname === void 0 ? void 0 : (_surname_charAt = surname.charAt(0)) === null || _surname_charAt === void 0 ? void 0 : _surname_charAt.toUpperCase()) || \"\";\n    return \"\".concat(first).concat(last) || \"??\";\n};\nconst getVesselInitials = (title)=>{\n    if (!title) return \"??\";\n    const words = title.split(\" \").filter((word)=>word.length > 0);\n    if (words.length === 1) {\n        return words[0].substring(0, 2).toUpperCase();\n    }\n    return words.slice(0, 2).map((word)=>word.charAt(0).toUpperCase()).join(\"\");\n};\nconst getCrewDetails = (assignedToID, crewInfo)=>{\n    return crewInfo === null || crewInfo === void 0 ? void 0 : crewInfo.find((crew)=>crew.id === assignedToID.toString());\n};\nconst getVesselDetails = (vesselID, vessels)=>{\n    return vessels === null || vessels === void 0 ? void 0 : vessels.find((vessel)=>vessel.id === vesselID);\n};\n// Unified maintenance table column factory\nconst createMaintenanceTableColumns = (config)=>{\n    const { showVessel = true, showMobileCards = false, crewInfo, vessels, getVesselWithIcon, pathname, searchParams } = config;\n    return (0,_components_filteredTable__WEBPACK_IMPORTED_MODULE_8__.createColumns)([\n        // Title Column\n        {\n            accessorKey: \"title\",\n            header: (param)=>{\n                let { column } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_9__.DataTableSortHeader, {\n                    column: column,\n                    title: \"Title\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                    lineNumber: 92,\n                    columnNumber: 17\n                }, undefined);\n            },\n            cell: (param)=>{\n                let { row } = param;\n                var _maintenanceCheck_isOverDue, _maintenanceCheck_basicComponent, _maintenanceCheck_assignedTo;\n                const maintenanceCheck = row.original;\n                const overDueStatus = (_maintenanceCheck_isOverDue = maintenanceCheck.isOverDue) === null || _maintenanceCheck_isOverDue === void 0 ? void 0 : _maintenanceCheck_isOverDue.status;\n                if (showMobileCards) {\n                    var _maintenanceCheck_assignedTo1, _maintenanceCheck_inventory, _maintenanceCheck_assignedTo2, _maintenanceCheck_basicComponent1;\n                    const crewDetails = getCrewDetails(((_maintenanceCheck_assignedTo1 = maintenanceCheck.assignedTo) === null || _maintenanceCheck_assignedTo1 === void 0 ? void 0 : _maintenanceCheck_assignedTo1.id) || 0, crewInfo);\n                    var _maintenanceCheck_name, _maintenanceCheck_name1;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_17__.Card, {\n                                className: \"xs:hidden space-y-3 p-2.5 min-h-20 w-full shadow-none rounded-none bg-transparent\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_17__.CardContent, {\n                                    className: \"p-0 space-y-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                            href: \"/maintenance?taskID=\".concat(maintenanceCheck.id, \"&redirect_to=\").concat(pathname, \"?\").concat(searchParams.toString()),\n                                            className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_21__.cn)(\"text-base\", overDueStatus === \"High\" ? \"text-cinnabar-500 hover:text-cinnabar-700\" : overDueStatus === \"Upcoming\" ? \"text-fire-bush-500 hover:text-fire-bush-700\" : \"hover:text-curious-blue-400\"),\n                                            children: (_maintenanceCheck_name = maintenanceCheck.name) !== null && _maintenanceCheck_name !== void 0 ? _maintenanceCheck_name : \"Task #\".concat(maintenanceCheck.id, \" (No Name) - \").concat(dayjs__WEBPACK_IMPORTED_MODULE_5___default()(maintenanceCheck.created).format(\"DD/MM/YYYY\"))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                            lineNumber: 109,\n                                            columnNumber: 37\n                                        }, undefined),\n                                        ((_maintenanceCheck_inventory = maintenanceCheck.inventory) === null || _maintenanceCheck_inventory === void 0 ? void 0 : _maintenanceCheck_inventory.id) > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-col\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xs text-muted-foreground\",\n                                                    children: \"Inventory item\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                                    lineNumber: 128,\n                                                    columnNumber: 45\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                    href: \"/inventory/view?id=\".concat(maintenanceCheck.inventory.id),\n                                                    className: \"hover:underline text-sm\",\n                                                    children: maintenanceCheck.inventory.item\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                                    lineNumber: 131,\n                                                    columnNumber: 45\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                            lineNumber: 127,\n                                            columnNumber: 41\n                                        }, undefined),\n                                        ((_maintenanceCheck_assignedTo2 = maintenanceCheck.assignedTo) === null || _maintenanceCheck_assignedTo2 === void 0 ? void 0 : _maintenanceCheck_assignedTo2.id) > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_12__.Avatar, {\n                                            variant: \"secondary\",\n                                            className: \"h-8 w-8\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_12__.AvatarFallback, {\n                                                className: \"text-xs\",\n                                                children: getCrewInitials(crewDetails === null || crewDetails === void 0 ? void 0 : crewDetails.firstName, crewDetails === null || crewDetails === void 0 ? void 0 : crewDetails.surname)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                                lineNumber: 147,\n                                                columnNumber: 45\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                            lineNumber: 144,\n                                            columnNumber: 41\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                    lineNumber: 108,\n                                    columnNumber: 33\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                lineNumber: 107,\n                                columnNumber: 29\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"hidden xs:block\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center flex-nowrap gap-2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                            href: \"/maintenance?taskID=\".concat(maintenanceCheck.id, \"&redirect_to=\").concat(pathname, \"?\").concat(searchParams.toString()),\n                                            className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_21__.cn)(\"text-base\", overDueStatus === \"High\" ? \"text-cinnabar-500 hover:text-cinnabar-700\" : overDueStatus === \"Upcoming\" ? \"text-fire-bush-500 hover:text-fire-bush-700\" : \"hover:text-curious-blue-400\"),\n                                            children: (_maintenanceCheck_name1 = maintenanceCheck.name) !== null && _maintenanceCheck_name1 !== void 0 ? _maintenanceCheck_name1 : \"Task #\".concat(maintenanceCheck.id, \" (No Name) - \").concat(dayjs__WEBPACK_IMPORTED_MODULE_5___default()(maintenanceCheck.created).format(\"DD/MM/YYYY\"))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                            lineNumber: 161,\n                                            columnNumber: 37\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                        lineNumber: 160,\n                                        columnNumber: 33\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"md:hidden\",\n                                        children: ((_maintenanceCheck_basicComponent1 = maintenanceCheck.basicComponent) === null || _maintenanceCheck_basicComponent1 === void 0 ? void 0 : _maintenanceCheck_basicComponent1.id) > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                            href: \"/vessel/info?id=\".concat(maintenanceCheck.basicComponent.id),\n                                            className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_21__.cn)(\"text-base\", overDueStatus === \"High\" ? \"text-cinnabar-500 hover:text-cinnabar-700\" : overDueStatus === \"Upcoming\" ? \"text-fire-bush-500 hover:text-fire-bush-700\" : \"hover:text-curious-blue-400\"),\n                                            children: maintenanceCheck.basicComponent.title\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                            lineNumber: 181,\n                                            columnNumber: 41\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                        lineNumber: 178,\n                                        columnNumber: 33\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                lineNumber: 159,\n                                columnNumber: 29\n                            }, undefined)\n                        ]\n                    }, void 0, true);\n                }\n                var _maintenanceCheck_name2;\n                // Simple layout for non-mobile-card version\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                href: \"/maintenance?taskID=\".concat(maintenanceCheck.id, \"&redirect_to=\").concat(pathname, \"?\").concat(searchParams.toString()),\n                                className: \"\".concat(overDueStatus === \"High\" ? \"text-cinnabar-500 hover:text-cinnabar-700\" : overDueStatus === \"Upcoming\" ? \"text-fire-bush-500 hover:text-fire-bush-700\" : \"hover:text-curious-blue-400\"),\n                                children: (_maintenanceCheck_name2 = maintenanceCheck.name) !== null && _maintenanceCheck_name2 !== void 0 ? _maintenanceCheck_name2 : \"Task #\".concat(maintenanceCheck.id, \" (No Name) - \").concat(dayjs__WEBPACK_IMPORTED_MODULE_5___default()(maintenanceCheck.created).format(\"DD/MM/YYYY\"))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                lineNumber: 208,\n                                columnNumber: 29\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                            lineNumber: 207,\n                            columnNumber: 25\n                        }, undefined),\n                        showVessel && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:hidden\",\n                            children: ((_maintenanceCheck_basicComponent = maintenanceCheck.basicComponent) === null || _maintenanceCheck_basicComponent === void 0 ? void 0 : _maintenanceCheck_basicComponent.id) > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                href: \"/vessel/info?id=\".concat(maintenanceCheck.basicComponent.id),\n                                className: \"text-sm text-muted-foreground hover:underline\",\n                                children: maintenanceCheck.basicComponent.title\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                lineNumber: 228,\n                                columnNumber: 37\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                            lineNumber: 226,\n                            columnNumber: 29\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"md:hidden space-y-2\",\n                            children: ((_maintenanceCheck_assignedTo = maintenanceCheck.assignedTo) === null || _maintenanceCheck_assignedTo === void 0 ? void 0 : _maintenanceCheck_assignedTo.id) > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-muted-foreground\",\n                                        children: [\n                                            \"Assigned to:\",\n                                            \" \"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                        lineNumber: 241,\n                                        columnNumber: 37\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        href: \"/crew/info?id=\".concat(maintenanceCheck.assignedTo.id),\n                                        className: \"hover:underline\",\n                                        children: maintenanceCheck.assignedTo.name\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                        lineNumber: 244,\n                                        columnNumber: 37\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                lineNumber: 240,\n                                columnNumber: 33\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                            lineNumber: 238,\n                            columnNumber: 25\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                    lineNumber: 206,\n                    columnNumber: 21\n                }, undefined);\n            },\n            sortingFn: (rowA, rowB)=>{\n                var _rowA_original, _rowB_original;\n                const valueA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : _rowA_original.name) || \"\";\n                const valueB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : _rowB_original.name) || \"\";\n                return valueA.localeCompare(valueB);\n            }\n        },\n        // Location/Vessel Column (conditional)\n        ...showVessel ? [\n            {\n                accessorKey: \"location\",\n                header: (param)=>{\n                    let { column } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_9__.DataTableSortHeader, {\n                        column: column,\n                        title: \"Location\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                        lineNumber: 271,\n                        columnNumber: 27\n                    }, undefined);\n                },\n                cellAlignment: \"left\",\n                breakpoint: showMobileCards ? \"laptop\" : \"landscape\",\n                cell: (param)=>{\n                    let { row } = param;\n                    var _maintenanceCheck_basicComponent, _maintenanceCheck_basicComponent1;\n                    const maintenanceCheck = row.original;\n                    const vesselDetails = getVesselDetails(((_maintenanceCheck_basicComponent = maintenanceCheck.basicComponent) === null || _maintenanceCheck_basicComponent === void 0 ? void 0 : _maintenanceCheck_basicComponent.id) || 0, vessels);\n                    if (showMobileCards && getVesselWithIcon) {\n                        var _maintenanceCheck_basicComponent2, _maintenanceCheck_basicComponent3;\n                        const vesselWithIcon = getVesselWithIcon(((_maintenanceCheck_basicComponent2 = maintenanceCheck.basicComponent) === null || _maintenanceCheck_basicComponent2 === void 0 ? void 0 : _maintenanceCheck_basicComponent2.id) || 0, vesselDetails);\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: ((_maintenanceCheck_basicComponent3 = maintenanceCheck.basicComponent) === null || _maintenanceCheck_basicComponent3 === void 0 ? void 0 : _maintenanceCheck_basicComponent3.id) > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2.5\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_13__.Tooltip, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_13__.TooltipTrigger, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"min-w-fit\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_vessels_vesel_icon__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                        vessel: vesselWithIcon\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                                        lineNumber: 301,\n                                                        columnNumber: 59\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                                    lineNumber: 300,\n                                                    columnNumber: 55\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                                lineNumber: 299,\n                                                columnNumber: 51\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_13__.TooltipContent, {\n                                                children: maintenanceCheck.basicComponent.title\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                                lineNumber: 308,\n                                                columnNumber: 51\n                                            }, undefined)\n                                        ]\n                                    }, vesselDetails === null || vesselDetails === void 0 ? void 0 : vesselDetails.id, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                        lineNumber: 298,\n                                        columnNumber: 47\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        href: \"/vessel/info?id=\".concat(maintenanceCheck.basicComponent.id),\n                                        className: \"hover:underline\",\n                                        children: maintenanceCheck.basicComponent.title\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                        lineNumber: 316,\n                                        columnNumber: 47\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                lineNumber: 297,\n                                columnNumber: 43\n                            }, undefined)\n                        }, void 0, false);\n                    }\n                    // Simple layout\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"hidden lg:block\",\n                        children: ((_maintenanceCheck_basicComponent1 = maintenanceCheck.basicComponent) === null || _maintenanceCheck_basicComponent1 === void 0 ? void 0 : _maintenanceCheck_basicComponent1.id) > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2.5\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_12__.Avatar, {\n                                    size: \"sm\",\n                                    variant: \"secondary\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_12__.AvatarFallback, {\n                                        className: \"text-xs\",\n                                        children: getVesselInitials((vesselDetails === null || vesselDetails === void 0 ? void 0 : vesselDetails.title) || maintenanceCheck.basicComponent.title || undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                        lineNumber: 336,\n                                        columnNumber: 47\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                    lineNumber: 335,\n                                    columnNumber: 43\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    href: \"/vessel/info?id=\".concat(maintenanceCheck.basicComponent.id),\n                                    className: \"hover:underline\",\n                                    children: maintenanceCheck.basicComponent.title\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                    lineNumber: 346,\n                                    columnNumber: 43\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                            lineNumber: 334,\n                            columnNumber: 39\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                        lineNumber: 332,\n                        columnNumber: 31\n                    }, undefined);\n                },\n                sortingFn: (rowA, rowB)=>{\n                    var _rowA_original_basicComponent, _rowA_original, _rowB_original_basicComponent, _rowB_original;\n                    const valueA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : (_rowA_original_basicComponent = _rowA_original.basicComponent) === null || _rowA_original_basicComponent === void 0 ? void 0 : _rowA_original_basicComponent.title) || \"\";\n                    const valueB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : (_rowB_original_basicComponent = _rowB_original.basicComponent) === null || _rowB_original_basicComponent === void 0 ? void 0 : _rowB_original_basicComponent.title) || \"\";\n                    return valueA.localeCompare(valueB);\n                }\n            }\n        ] : [],\n        // Assigned To Column\n        {\n            accessorKey: \"assigned\",\n            header: (param)=>{\n                let { column } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_9__.DataTableSortHeader, {\n                    column: column,\n                    title: \"Assigned to\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                    lineNumber: 377,\n                    columnNumber: 17\n                }, undefined);\n            },\n            cellAlignment: \"left\",\n            breakpoint: showMobileCards ? \"tablet-md\" : \"landscape\",\n            cell: (param)=>{\n                let { row } = param;\n                var _maintenanceCheck_assignedTo, _maintenanceCheck_assignedTo1;\n                const maintenanceCheck = row.original;\n                const crewDetails = getCrewDetails(((_maintenanceCheck_assignedTo = maintenanceCheck.assignedTo) === null || _maintenanceCheck_assignedTo === void 0 ? void 0 : _maintenanceCheck_assignedTo.id) || 0, crewInfo);\n                const displayClass = showMobileCards ? \"hidden md:block\" : \"hidden md:block\";\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: displayClass,\n                    children: ((_maintenanceCheck_assignedTo1 = maintenanceCheck.assignedTo) === null || _maintenanceCheck_assignedTo1 === void 0 ? void 0 : _maintenanceCheck_assignedTo1.id) > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2.5\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_12__.Avatar, {\n                                variant: \"secondary\",\n                                className: \"h-8 w-8\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_12__.AvatarFallback, {\n                                    className: \"text-xs\",\n                                    children: getCrewInitials(crewDetails === null || crewDetails === void 0 ? void 0 : crewDetails.firstName, crewDetails === null || crewDetails === void 0 ? void 0 : crewDetails.surname)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                    lineNumber: 399,\n                                    columnNumber: 37\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                lineNumber: 398,\n                                columnNumber: 33\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                href: \"/crew/info?id=\".concat(maintenanceCheck.assignedTo.id),\n                                className: \"hover:underline \".concat(showMobileCards ? \"hidden tablet-md:block\" : \"hidden lg:block\"),\n                                children: maintenanceCheck.assignedTo.name\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                lineNumber: 406,\n                                columnNumber: 33\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                        lineNumber: 397,\n                        columnNumber: 29\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                    lineNumber: 395,\n                    columnNumber: 21\n                }, undefined);\n            },\n            sortingFn: (rowA, rowB)=>{\n                var _rowA_original_assignedTo, _rowA_original, _rowB_original_assignedTo, _rowB_original;\n                const valueA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : (_rowA_original_assignedTo = _rowA_original.assignedTo) === null || _rowA_original_assignedTo === void 0 ? void 0 : _rowA_original_assignedTo.name) || \"\";\n                const valueB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : (_rowB_original_assignedTo = _rowB_original.assignedTo) === null || _rowB_original_assignedTo === void 0 ? void 0 : _rowB_original_assignedTo.name) || \"\";\n                return valueA.localeCompare(valueB);\n            }\n        },\n        // Inventory Column\n        {\n            accessorKey: \"inventory\",\n            header: \"Inventory item\",\n            cellAlignment: \"left\",\n            breakpoint: showMobileCards ? \"phablet\" : \"tablet-lg\",\n            cell: (param)=>{\n                let { row } = param;\n                var _maintenanceCheck_inventory;\n                const maintenanceCheck = row.original;\n                const displayClass = showMobileCards ? \"\" : \"hidden md:block\";\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: displayClass,\n                    children: ((_maintenanceCheck_inventory = maintenanceCheck.inventory) === null || _maintenanceCheck_inventory === void 0 ? void 0 : _maintenanceCheck_inventory.id) > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        href: \"/inventory/view?id=\".concat(maintenanceCheck.inventory.id),\n                        className: \"hover:underline\",\n                        children: maintenanceCheck.inventory.item\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                        lineNumber: 445,\n                        columnNumber: 29\n                    }, undefined) : showMobileCards && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        children: \"-\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                        lineNumber: 451,\n                        columnNumber: 48\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                    lineNumber: 443,\n                    columnNumber: 21\n                }, undefined);\n            }\n        },\n        // Status Column\n        {\n            accessorKey: \"status\",\n            header: (param)=>{\n                let { column } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_9__.DataTableSortHeader, {\n                    column: column,\n                    title: \"Status\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                    lineNumber: 462,\n                    columnNumber: 17\n                }, undefined);\n            },\n            cellAlignment: \"right\",\n            cell: (param)=>{\n                let { row } = param;\n                const maintenanceCheck = row.original;\n                if (!maintenanceCheck) {\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: \"-\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                        lineNumber: 469,\n                        columnNumber: 28\n                    }, undefined);\n                }\n                if (showMobileCards) {\n                    var _maintenanceCheck_isOverDue, _maintenanceCheck_isOverDue1;\n                    const overDueStatus = (_maintenanceCheck_isOverDue = maintenanceCheck.isOverDue) === null || _maintenanceCheck_isOverDue === void 0 ? void 0 : _maintenanceCheck_isOverDue.status;\n                    const overDueDays = (_maintenanceCheck_isOverDue1 = maintenanceCheck.isOverDue) === null || _maintenanceCheck_isOverDue1 === void 0 ? void 0 : _maintenanceCheck_isOverDue1.day;\n                    const bp = (0,_components_hooks_useBreakpoints__WEBPACK_IMPORTED_MODULE_16__.useBreakpoints)();\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: overDueStatus === \"High\" ? !bp[\"tablet-lg\"] ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"items-end mr-2.5 text-nowrap w-fit \".concat(overDueStatus === \"High\" ? \"alert text-sm !px-1.5 !py-0.5 xs:px-3 xs:py-1 whitespace-nowrap\" : \"\"),\n                            children: overDueDays * -1 + \" days ago\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                            lineNumber: 481,\n                            columnNumber: 37\n                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatusBadge, {\n                            maintenanceCheck: maintenanceCheck\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                            lineNumber: 490,\n                            columnNumber: 37\n                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatusBadge, {\n                            maintenanceCheck: maintenanceCheck\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                            lineNumber: 495,\n                            columnNumber: 33\n                        }, undefined)\n                    }, void 0, false);\n                }\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"hidden md:block\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatusBadge, {\n                        maintenanceCheck: maintenanceCheck\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                        lineNumber: 505,\n                        columnNumber: 25\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                    lineNumber: 504,\n                    columnNumber: 21\n                }, undefined);\n            },\n            sortingFn: (rowA, rowB)=>{\n                var _rowA_original_isOverDue, _rowA_original, _rowB_original_isOverDue, _rowB_original;\n                const valueA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : (_rowA_original_isOverDue = _rowA_original.isOverDue) === null || _rowA_original_isOverDue === void 0 ? void 0 : _rowA_original_isOverDue.days) || \"\";\n                const valueB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : (_rowB_original_isOverDue = _rowB_original.isOverDue) === null || _rowB_original_isOverDue === void 0 ? void 0 : _rowB_original_isOverDue.days) || \"\";\n                return valueA.localeCompare(valueB);\n            }\n        }\n    ]);\n};\n// Status badge component following UI standards\nconst StatusBadge = (param)=>{\n    let { maintenanceCheck } = param;\n    var _maintenanceCheck_isOverDue, _maintenanceCheck_isOverDue1, _maintenanceCheck_isOverDue2, _maintenanceCheck_isOverDue3, _maintenanceCheck_isOverDue4, _maintenanceCheck_isOverDue5, _maintenanceCheck_isOverDue6, _maintenanceCheck_isOverDue7, _maintenanceCheck_isOverDue8, _maintenanceCheck_isOverDue9;\n    const isOverdue = (maintenanceCheck === null || maintenanceCheck === void 0 ? void 0 : (_maintenanceCheck_isOverDue = maintenanceCheck.isOverDue) === null || _maintenanceCheck_isOverDue === void 0 ? void 0 : _maintenanceCheck_isOverDue.status) === \"High\";\n    // Get status text\n    let statusText = \"\";\n    if ((maintenanceCheck === null || maintenanceCheck === void 0 ? void 0 : (_maintenanceCheck_isOverDue1 = maintenanceCheck.isOverDue) === null || _maintenanceCheck_isOverDue1 === void 0 ? void 0 : _maintenanceCheck_isOverDue1.status) && [\n        \"High\",\n        \"Medium\",\n        \"Low\"\n    ].includes(maintenanceCheck.isOverDue.status)) {\n        var _maintenanceCheck_isOverDue10;\n        statusText = maintenanceCheck === null || maintenanceCheck === void 0 ? void 0 : (_maintenanceCheck_isOverDue10 = maintenanceCheck.isOverDue) === null || _maintenanceCheck_isOverDue10 === void 0 ? void 0 : _maintenanceCheck_isOverDue10.days;\n    } else if ((maintenanceCheck === null || maintenanceCheck === void 0 ? void 0 : (_maintenanceCheck_isOverDue2 = maintenanceCheck.isOverDue) === null || _maintenanceCheck_isOverDue2 === void 0 ? void 0 : _maintenanceCheck_isOverDue2.status) === \"Completed\" && (maintenanceCheck === null || maintenanceCheck === void 0 ? void 0 : (_maintenanceCheck_isOverDue3 = maintenanceCheck.isOverDue) === null || _maintenanceCheck_isOverDue3 === void 0 ? void 0 : _maintenanceCheck_isOverDue3.days) === \"Save As Draft\") {\n        var _maintenanceCheck_isOverDue11;\n        statusText = maintenanceCheck === null || maintenanceCheck === void 0 ? void 0 : (_maintenanceCheck_isOverDue11 = maintenanceCheck.isOverDue) === null || _maintenanceCheck_isOverDue11 === void 0 ? void 0 : _maintenanceCheck_isOverDue11.days;\n    } else if ((maintenanceCheck === null || maintenanceCheck === void 0 ? void 0 : (_maintenanceCheck_isOverDue4 = maintenanceCheck.isOverDue) === null || _maintenanceCheck_isOverDue4 === void 0 ? void 0 : _maintenanceCheck_isOverDue4.status) === \"Upcoming\") {\n        var _maintenanceCheck_isOverDue12;\n        statusText = maintenanceCheck === null || maintenanceCheck === void 0 ? void 0 : (_maintenanceCheck_isOverDue12 = maintenanceCheck.isOverDue) === null || _maintenanceCheck_isOverDue12 === void 0 ? void 0 : _maintenanceCheck_isOverDue12.days;\n    } else if ((maintenanceCheck === null || maintenanceCheck === void 0 ? void 0 : (_maintenanceCheck_isOverDue5 = maintenanceCheck.isOverDue) === null || _maintenanceCheck_isOverDue5 === void 0 ? void 0 : _maintenanceCheck_isOverDue5.status) === \"Completed\" && lodash_isEmpty__WEBPACK_IMPORTED_MODULE_6___default()(maintenanceCheck === null || maintenanceCheck === void 0 ? void 0 : (_maintenanceCheck_isOverDue6 = maintenanceCheck.isOverDue) === null || _maintenanceCheck_isOverDue6 === void 0 ? void 0 : _maintenanceCheck_isOverDue6.days)) {\n        var _maintenanceCheck_isOverDue13;\n        statusText = maintenanceCheck === null || maintenanceCheck === void 0 ? void 0 : (_maintenanceCheck_isOverDue13 = maintenanceCheck.isOverDue) === null || _maintenanceCheck_isOverDue13 === void 0 ? void 0 : _maintenanceCheck_isOverDue13.status;\n    } else if ((maintenanceCheck === null || maintenanceCheck === void 0 ? void 0 : (_maintenanceCheck_isOverDue7 = maintenanceCheck.isOverDue) === null || _maintenanceCheck_isOverDue7 === void 0 ? void 0 : _maintenanceCheck_isOverDue7.status) === \"Completed\" && !lodash_isEmpty__WEBPACK_IMPORTED_MODULE_6___default()(maintenanceCheck === null || maintenanceCheck === void 0 ? void 0 : (_maintenanceCheck_isOverDue8 = maintenanceCheck.isOverDue) === null || _maintenanceCheck_isOverDue8 === void 0 ? void 0 : _maintenanceCheck_isOverDue8.days) && (maintenanceCheck === null || maintenanceCheck === void 0 ? void 0 : (_maintenanceCheck_isOverDue9 = maintenanceCheck.isOverDue) === null || _maintenanceCheck_isOverDue9 === void 0 ? void 0 : _maintenanceCheck_isOverDue9.days) !== \"Save As Draft\") {\n        var _maintenanceCheck_isOverDue14;\n        statusText = maintenanceCheck === null || maintenanceCheck === void 0 ? void 0 : (_maintenanceCheck_isOverDue14 = maintenanceCheck.isOverDue) === null || _maintenanceCheck_isOverDue14 === void 0 ? void 0 : _maintenanceCheck_isOverDue14.days;\n    }\n    // Only apply styling to overdue items, others are plain text\n    if (isOverdue) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n            className: \"alert w-fit inline-block rounded-md text-nowrap mr-2.5 text-sm xs:text-base !px-1.5 !py-0.5 xs:px-3 xs:py-1\",\n            children: statusText\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n            lineNumber: 625,\n            columnNumber: 13\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n        className: \"text-nowrap text-sm xs:text-base mr-2.5 !px-1.5 !py-0.5 xs:px-3 xs:py-1\",\n        children: statusText\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n        lineNumber: 632,\n        columnNumber: 9\n    }, undefined);\n};\n_c = StatusBadge;\n// Reusable MaintenanceTable component that accepts props\nfunction MaintenanceTable(param) {\n    let { maintenanceChecks, vessels, crewInfo, showVessel = false } = param;\n    _s();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_7__.usePathname)();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_7__.useSearchParams)();\n    const { getVesselWithIcon } = (0,_app_lib_vessel_icon_helper__WEBPACK_IMPORTED_MODULE_15__.useVesselIconData)();\n    const columns = createMaintenanceTableColumns({\n        showVessel: !showVessel,\n        showMobileCards: false,\n        crewInfo,\n        vessels,\n        getVesselWithIcon,\n        pathname,\n        searchParams\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_filteredTable__WEBPACK_IMPORTED_MODULE_8__.DataTable, {\n        columns: columns,\n        data: maintenanceChecks,\n        pageSize: 20,\n        showToolbar: false\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n        lineNumber: 665,\n        columnNumber: 9\n    }, this);\n}\n_s(MaintenanceTable, \"BtAwTu7MooJrudiBzqv5a/psZQo=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_7__.usePathname,\n        next_navigation__WEBPACK_IMPORTED_MODULE_7__.useSearchParams,\n        _app_lib_vessel_icon_helper__WEBPACK_IMPORTED_MODULE_15__.useVesselIconData\n    ];\n});\n_c1 = MaintenanceTable;\nfunction TaskList() {\n    _s1();\n    var _s = $RefreshSig$();\n    const [maintenanceChecks, setMaintenanceChecks] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    const [filteredMaintenanceChecks, setFilteredMaintenanceChecks] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    const [vessels, setVessels] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    const [crewInfo, setCrewInfo] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    const [filter, setFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [keywordFilter, setKeywordFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_7__.usePathname)();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_7__.useSearchParams)();\n    const { getVesselWithIcon } = (0,_app_lib_vessel_icon_helper__WEBPACK_IMPORTED_MODULE_15__.useVesselIconData)();\n    const [queryMaintenanceChecks] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_22__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_2__.GET_MAINTENANCE_CHECK_LIST, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            const data = response.readComponentMaintenanceCheckList[0].list;\n            if (data) {\n                handleSetMaintenanceChecks(data);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"queryMaintenanceChecks error\", error);\n        }\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isLoading) {\n            loadMaintenanceChecks();\n            setIsLoading(false);\n        }\n    }, [\n        isLoading\n    ]);\n    const loadMaintenanceChecks = async ()=>{\n        await queryMaintenanceChecks({\n            variables: {\n                inventoryID: 0,\n                vesselID: 0\n            }\n        });\n    };\n    const handleSetVessels = (vessels)=>{\n        const activeVessels = vessels.filter((vessel)=>!vessel.archived);\n        const appendedData = activeVessels.map((item)=>({\n                ...item\n            }));\n        appendedData.push({\n            title: \"Other\",\n            id: 0\n        });\n        setVessels(appendedData);\n    };\n    const getVesselList = function(handleSetVessels) {\n        let offline = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : false;\n        _s();\n        const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n        const vesselModel = new _app_offline_models_vessel__WEBPACK_IMPORTED_MODULE_20__[\"default\"]();\n        (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n            if (isLoading) {\n                loadVessels();\n                setIsLoading(false);\n            }\n        }, [\n            isLoading\n        ]);\n        const [queryVessels] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_22__.useLazyQuery)(_queries__WEBPACK_IMPORTED_MODULE_19__.ReadVessels, {\n            fetchPolicy: \"cache-and-network\",\n            onCompleted: (queryVesselResponse)=>{\n                if (queryVesselResponse.readVessels.nodes) {\n                    handleSetVessels(queryVesselResponse.readVessels.nodes);\n                }\n            },\n            onError: (error)=>{\n                console.error(\"queryVessels error\", error);\n            }\n        });\n        const loadVessels = async ()=>{\n            if (offline) {\n                const response = await vesselModel.getAll();\n                handleSetVessels(response);\n            } else {\n                await queryVessels({\n                    variables: {\n                        limit: 200,\n                        offset: 0\n                    }\n                });\n            }\n        };\n    };\n    _s(getVesselList, \"pQOK42e7v9ItR64U9CP/qx2cQME=\", false, function() {\n        return [\n            _apollo_client__WEBPACK_IMPORTED_MODULE_22__.useLazyQuery\n        ];\n    });\n    getVesselList(handleSetVessels);\n    const handleSetMaintenanceChecks = (tasks)=>{\n        setMaintenanceChecks(tasks);\n        setFilteredMaintenanceChecks(tasks);\n        const appendedData = Array.from(new Set(tasks.filter((item)=>item.assignedTo.id > 0).map((item)=>item.assignedTo.id)));\n        loadCrewMemberInfo(appendedData);\n    };\n    const [queryCrewMemberInfo] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_22__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_2__.GET_CREW_BY_IDS, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            const data = response.readSeaLogsMembers.nodes;\n            if (data) {\n                setCrewInfo(data);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"queryCrewMemberInfo error\", error);\n        }\n    });\n    const loadCrewMemberInfo = async (crewId)=>{\n        await queryCrewMemberInfo({\n            variables: {\n                crewMemberIDs: crewId.length > 0 ? crewId : [\n                    0\n                ]\n            }\n        });\n    };\n    const handleFilterOnChange = (param)=>{\n        let { type, data } = param;\n        const searchFilter = {\n            ...filter\n        };\n        let filteredTasks = maintenanceChecks || [];\n        // Vessel filter\n        if (type === \"vessel\") {\n            if (Array.isArray(data) && data.length > 0) {\n                searchFilter.basicComponentID = {\n                    in: data.map((item)=>+item.value)\n                };\n            } else if (data && !Array.isArray(data)) {\n                searchFilter.basicComponentID = {\n                    eq: +data.value\n                };\n            } else {\n                delete searchFilter.basicComponentID;\n            }\n        }\n        // Status filter\n        if (type === \"status\") {\n            if (Array.isArray(data) && data.length > 0) {\n                searchFilter.status = {\n                    in: data.map((item)=>item.value)\n                };\n            } else if (data && !Array.isArray(data)) {\n                searchFilter.status = {\n                    eq: data.value\n                };\n            } else {\n                delete searchFilter.status;\n            }\n        }\n        // Assigned member filter\n        if (type === \"member\") {\n            if (Array.isArray(data) && data.length > 0) {\n                searchFilter.assignedToID = {\n                    in: data.map((item)=>+item.value)\n                };\n            } else if (data && !Array.isArray(data)) {\n                searchFilter.assignedToID = {\n                    eq: +data.value\n                };\n            } else {\n                delete searchFilter.assignedToID;\n            }\n        }\n        // Date range\n        if (type === \"dateRange\") {\n            if ((data === null || data === void 0 ? void 0 : data.startDate) && (data === null || data === void 0 ? void 0 : data.endDate)) {\n                searchFilter.expires = {\n                    gte: data.startDate,\n                    lte: data.endDate\n                };\n            } else {\n                delete searchFilter.expires;\n            }\n        }\n        // Category\n        if (type === \"category\") {\n            if (Array.isArray(data) && data.length > 0) {\n                searchFilter.maintenanceCategoryID = {\n                    in: data.map((item)=>+item.value)\n                };\n            } else if (data && !Array.isArray(data)) {\n                searchFilter.maintenanceCategoryID = {\n                    eq: +data.value\n                };\n            } else {\n                delete searchFilter.maintenanceCategoryID;\n            }\n        }\n        // Recurring filter - handle client-side filtering\n        let recurringFilter = null;\n        if (type === \"recurring\") {\n            if (data && !Array.isArray(data)) {\n                recurringFilter = data.value;\n            }\n        }\n        // Keyword filter\n        let keyFilter = keywordFilter;\n        if (type === \"keyword\" || keyFilter && keyFilter.length > 0) {\n            var _data_value;\n            const keyword = data === null || data === void 0 ? void 0 : (_data_value = data.value) === null || _data_value === void 0 ? void 0 : _data_value.trim().toLowerCase();\n            if (keyword && keyword.length > 0) {\n                filteredTasks = filteredTasks.filter((maintenanceCheck)=>[\n                        maintenanceCheck.name,\n                        maintenanceCheck.comments,\n                        maintenanceCheck.workOrderNumber\n                    ].some((field)=>field === null || field === void 0 ? void 0 : field.toLowerCase().includes(keyword)));\n                keyFilter = data.value;\n            } else {\n                keyFilter = null;\n            }\n        }\n        // Filtering based on current searchFilter\n        // Filter by vessel (basicComponentID)\n        if (searchFilter.basicComponentID) {\n            const ids = searchFilter.basicComponentID.in || [\n                searchFilter.basicComponentID.eq\n            ];\n            filteredTasks = filteredTasks.filter((mc)=>{\n                var _mc_basicComponent;\n                return ids.includes((_mc_basicComponent = mc.basicComponent) === null || _mc_basicComponent === void 0 ? void 0 : _mc_basicComponent.id);\n            });\n        }\n        // Filter by status\n        if (searchFilter.status) {\n            const statuses = searchFilter.status.in || [\n                searchFilter.status.eq\n            ];\n            filteredTasks = filteredTasks.filter((mc)=>statuses.includes(mc.status));\n        }\n        // Filter by assignedToID\n        if (searchFilter.assignedToID) {\n            const ids = searchFilter.assignedToID.in || [\n                searchFilter.assignedToID.eq\n            ];\n            filteredTasks = filteredTasks.filter((mc)=>{\n                var _mc_assignedTo;\n                return ids.includes((_mc_assignedTo = mc.assignedTo) === null || _mc_assignedTo === void 0 ? void 0 : _mc_assignedTo.id);\n            });\n        }\n        // Filter by category\n        if (searchFilter.maintenanceCategoryID) {\n            const ids = searchFilter.maintenanceCategoryID.in || [\n                searchFilter.maintenanceCategoryID.eq\n            ];\n            filteredTasks = filteredTasks.filter((mc)=>ids.includes(mc.maintenanceCategoryID));\n        }\n        // Filter by date range\n        if (searchFilter.expires && searchFilter.expires.gte && searchFilter.expires.lte) {\n            filteredTasks = filteredTasks.filter((mc)=>dayjs__WEBPACK_IMPORTED_MODULE_5___default()(mc.startDate).isAfter(dayjs__WEBPACK_IMPORTED_MODULE_5___default()(searchFilter.expires.gte)) && dayjs__WEBPACK_IMPORTED_MODULE_5___default()(mc.startDate).isBefore(dayjs__WEBPACK_IMPORTED_MODULE_5___default()(searchFilter.expires.lte)));\n        }\n        // Filter by recurring status\n        if (recurringFilter) {\n            if (recurringFilter === \"recurring\") {\n                // Recurring tasks have recurringID > 0\n                filteredTasks = filteredTasks.filter((mc)=>mc.recurringID > 0);\n            } else if (recurringFilter === \"one-off\") {\n                // One-off tasks have recurringID = 0 or null\n                filteredTasks = filteredTasks.filter((mc)=>!mc.recurringID || mc.recurringID === 0);\n            }\n        }\n        // Set updated filters\n        setFilter(searchFilter);\n        setKeywordFilter(keyFilter);\n        setFilteredMaintenanceChecks(filteredTasks);\n    // Optionally call API: loadMaintenanceChecks(searchFilter, keyFilter);\n    };\n    // Use the unified column factory for the main table\n    const columns = createMaintenanceTableColumns({\n        showVessel: true,\n        showMobileCards: true,\n        crewInfo: crewInfo || [],\n        vessels: vessels || [],\n        getVesselWithIcon,\n        pathname,\n        searchParams\n    });\n    // Row status evaluator for maintenance tasks\n    const getMaintenanceRowStatus = (maintenanceCheck)=>{\n        var _maintenanceCheck_isOverDue;\n        // Skip completed, archived, or draft tasks\n        if (maintenanceCheck.status === \"Completed\" || maintenanceCheck.archived || maintenanceCheck.status === \"Save_As_Draft\") {\n            return \"normal\";\n        }\n        const overDueStatus = (_maintenanceCheck_isOverDue = maintenanceCheck.isOverDue) === null || _maintenanceCheck_isOverDue === void 0 ? void 0 : _maintenanceCheck_isOverDue.status;\n        // Use the pre-calculated status values from the system\n        switch(overDueStatus){\n            case \"High\":\n                return \"overdue\" // Red highlighting\n                ;\n            case \"Upcoming\":\n                return \"upcoming\" // Orange highlighting\n                ;\n            case \"Medium\":\n            case \"Open\":\n            default:\n                return \"normal\" // No highlighting\n                ;\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_list_header__WEBPACK_IMPORTED_MODULE_11__.ListHeader, {\n                title: \"Maintenance\",\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_lib_icons__WEBPACK_IMPORTED_MODULE_18__.SealogsMaintenanceIcon, {\n                    className: \"h-12 w-12 ring-1 p-1 rounded-full bg-[#fff]\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                    lineNumber: 1011,\n                    columnNumber: 21\n                }, void 0),\n                actions: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_filter_components_maintenance_actions__WEBPACK_IMPORTED_MODULE_10__.MaintenanceFilterActions, {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                    lineNumber: 1013,\n                    columnNumber: 26\n                }, void 0),\n                titleClassName: \"\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                lineNumber: 1008,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-16\",\n                children: maintenanceChecks && vessels ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_filteredTable__WEBPACK_IMPORTED_MODULE_8__.DataTable, {\n                    columns: columns,\n                    data: filteredMaintenanceChecks || [],\n                    pageSize: 20,\n                    onChange: handleFilterOnChange,\n                    rowStatus: getMaintenanceRowStatus\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                    lineNumber: 1018,\n                    columnNumber: 21\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_skeletons__WEBPACK_IMPORTED_MODULE_3__.TableSkeleton, {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                    lineNumber: 1026,\n                    columnNumber: 21\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                lineNumber: 1016,\n                columnNumber: 13\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s1(TaskList, \"uzr//CfjeeOC6i2osXWwrtVInDQ=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_7__.usePathname,\n        next_navigation__WEBPACK_IMPORTED_MODULE_7__.useSearchParams,\n        _app_lib_vessel_icon_helper__WEBPACK_IMPORTED_MODULE_15__.useVesselIconData,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_22__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_22__.useLazyQuery\n    ];\n});\n_c2 = TaskList;\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"StatusBadge\");\n$RefreshReg$(_c1, \"MaintenanceTable\");\n$RefreshReg$(_c2, \"TaskList\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/maintenance/list/list.tsx\n"));

/***/ })

});